{"name": "web-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start-backend": "node server.cjs", "start-server": "node server.cjs", "debug-auth": "node debug-auth.js"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "cors": "^2.8.5", "express": "^4.17.1", "knex": "^3.1.0", "multer": "^1.4.3", "sqlite3": "^5.1.7", "bootstrap": "^5.3.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}
// 后台管理系统 API 配置文件

// 环境配置 - 使用Vite代理，无需指定完整URL
const ENV_CONFIG = {
  development: {
    API_BASE_URL: '',  // 🔥 使用Vite代理，空字符串表示相对路径
    WS_BASE_URL: 'ws://localhost:8000'      // WebSocket暂不支持，可忽略
  },
  production: {
    API_BASE_URL: '',  // 🔥 生产环境使用相对路径，通过Nginx代理
    WS_BASE_URL: 'wss://your-domain.com'      // WebSocket暂不支持，可忽略
  }
};

// 获取当前环境配置
function getEnvConfig() {
  const env = import.meta.env.MODE || 'development';
  return ENV_CONFIG[env] || ENV_CONFIG.development;
}

const config = getEnvConfig();

// API 端点配置
export const API_ENDPOINTS = {
  // 基础配置
  BASE_URL: config.API_BASE_URL,
  WS_BASE_URL: config.WS_BASE_URL,
  
  // 系统接口
  HEALTH: '/api/health',
  
  // 认证接口
  AUTH: {
    LOGIN: '/api/admin/login',
    LOGOUT: '/api/admin/logout',
    REFRESH: '/api/admin/refresh',
    CHECK: '/api/admin/check'
  },
  
  // 视频管理接口
  VIDEOS: {
    LIST: '/api/admin/videos',
    DETAIL: (id) => `/api/admin/videos/${id}`,
    REVIEW: '/api/admin/review',
    DELETE: (id) => `/api/admin/videos/${id}`,
    BATCH_REVIEW: '/api/admin/videos/batch-review',
    EXPORT: '/api/admin/videos/export'
  },
  
  // 统计接口
  STATS: {
    OVERVIEW: '/api/admin/stats',
    DAILY: '/api/admin/stats/daily',
    MONTHLY: '/api/admin/stats/monthly',
    REVIEWER: '/api/admin/stats/reviewer'
  },
  
  // 审核日志接口 - 已更新为PHP后台端点
  LOGS: {
    VIDEO: (videoId) => `/api/admin/review-logs?videoId=${videoId}`,
    REVIEWER: (reviewerId) => `/api/admin/review-logs?reviewerId=${reviewerId}`,
    LIST: '/api/admin/review-logs',
    EXPORT: '/api/admin/review-logs/export'  // 暂不支持
  },
  
  // 管理员管理接口 - PHP后台暂不支持，使用基础功能
  ADMINS: {
    LIST: '/api/admin/admins',      // 暂不支持
    CREATE: '/api/admin/admins',    // 暂不支持
    UPDATE: (id) => `/api/admin/admins/${id}`,    // 暂不支持
    DELETE: (id) => `/api/admin/admins/${id}`,    // 暂不支持
    RESET_PASSWORD: (id) => `/api/admin/admins/${id}/reset-password`  // 暂不支持
  },
  
  // 系统设置接口 - 已更新为PHP后台端点
  SETTINGS: {
    GET: '/api/admin/settings',
    UPDATE: '/api/admin/settings',
    EXPIRED_STATUS: '/api/admin/expired-status',  // 新增：检查过期状态
    BACKUP: '/api/admin/settings/backup',         // 暂不支持
    RESTORE: '/api/admin/settings/restore'        // 暂不支持
  },
  
  // 文件上传接口
  UPLOAD: {
    AVATAR: '/api/admin/upload/avatar',
    EXPORT: '/api/admin/upload/export'
  }
};

// 小程序接口（供参考）
export const MINIPROGRAM_ENDPOINTS = {
  BASE_URL: config.API_BASE_URL,
  
  // 用户接口
  USER: {
    UPLOAD: '/api/upload',
    MY_VIDEOS: '/api/my/videos',
    VIDEO_DETAIL: (id) => `/api/my/videos/${id}`
  }
};

// 请求配置
export const REQUEST_CONFIG = {
  // 默认超时时间
  TIMEOUT: 10000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 请求头
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 认证头
  getAuthHeaders: () => {
    const token = localStorage.getItem('adminToken');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }
};

// 构建完整URL
export function buildUrl(endpoint, params = {}) {
  let url = `${API_ENDPOINTS.BASE_URL}${endpoint}`;
  
  // 添加查询参数
  if (Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, value);
      }
    });
    const queryString = searchParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }
  
  return url;
}

// 环境信息
export const ENV_INFO = {
  isDevelopment: import.meta.env.MODE === 'development',
  isProduction: import.meta.env.MODE === 'production',
  currentEnv: import.meta.env.MODE || 'development',
  apiBaseUrl: config.API_BASE_URL
};

// 导出默认配置
export default {
  API_ENDPOINTS,
  MINIPROGRAM_ENDPOINTS,
  REQUEST_CONFIG,
  buildUrl,
  ENV_INFO
};

// 统一的HTTP请求工具
import { API_ENDPOINTS, REQUEST_CONFIG, buildUrl } from '../config/api.js';

// 请求拦截器
class RequestInterceptor {
  constructor() {
    this.requestInterceptors = [];
    this.responseInterceptors = [];
  }

  // 添加请求拦截器
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  // 执行请求拦截器
  async executeRequestInterceptors(config) {
    let result = config;
    for (const interceptor of this.requestInterceptors) {
      result = await interceptor(result);
    }
    return result;
  }

  // 执行响应拦截器
  async executeResponseInterceptors(response) {
    let result = response;
    for (const interceptor of this.responseInterceptors) {
      result = await interceptor(result);
    }
    return result;
  }
}

// 创建拦截器实例
const interceptor = new RequestInterceptor();

// 默认请求拦截器 - 添加认证头
interceptor.addRequestInterceptor(async (config) => {
  const authHeaders = REQUEST_CONFIG.getAuthHeaders();
  config.headers = {
    ...REQUEST_CONFIG.HEADERS,
    ...authHeaders,
    ...config.headers
  };
  return config;
});

// 默认响应拦截器 - 处理认证失败
interceptor.addResponseInterceptor(async (response) => {
  if (response.status === 401 || response.status === 403) {
    // 清除token并跳转到登录页
    localStorage.removeItem('adminToken');
    window.location.href = '/login';
    throw new Error('认证失败，请重新登录');
  }
  return response;
});

// 基础请求函数
async function baseRequest(url, options = {}) {
  const config = {
    method: 'GET',
    headers: {},
    ...options
  };

  try {
    // 执行请求拦截器
    const finalConfig = await interceptor.executeRequestInterceptors(config);

    // 发送请求
    const response = await fetch(url, finalConfig);

    // 执行响应拦截器
    await interceptor.executeResponseInterceptors(response);

    // 处理响应
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
}

// GET 请求
export async function get(endpoint, params = {}, options = {}) {
  const url = buildUrl(endpoint, params);
  return baseRequest(url, { method: 'GET', ...options });
}

// POST 请求
export async function post(endpoint, data = {}, options = {}) {
  const url = buildUrl(endpoint);
  return baseRequest(url, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options
  });
}

// PUT 请求
export async function put(endpoint, data = {}, options = {}) {
  const url = buildUrl(endpoint);
  return baseRequest(url, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options
  });
}

// DELETE 请求
export async function del(endpoint, options = {}) {
  const url = buildUrl(endpoint);
  return baseRequest(url, { method: 'DELETE', ...options });
}

// 文件上传请求
export async function upload(endpoint, formData, onProgress = null, options = {}) {
  const url = buildUrl(endpoint);
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    // 设置请求头（不包含Content-Type，让浏览器自动设置）
    const authHeaders = REQUEST_CONFIG.getAuthHeaders();
    Object.entries(authHeaders).forEach(([key, value]) => {
      xhr.setRequestHeader(key, value);
    });
    
    // 监听上传进度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100);
          onProgress(progress);
        }
      });
    }
    
    // 监听请求完成
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          resolve(xhr.responseText);
        }
      } else {
        reject(new Error(`Upload failed: ${xhr.status}`));
      }
    });
    
    // 监听请求错误
    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed'));
    });
    
    // 发送请求
    xhr.open('POST', url);
    xhr.send(formData);
  });
}

// API 服务类
export class ApiService {
  // 健康检查
  static async checkHealth() {
    return get(API_ENDPOINTS.HEALTH);
  }

  // 认证相关
  static async login(username, password) {
    return post(API_ENDPOINTS.AUTH.LOGIN, { username, password });
  }

  static async logout() {
    return post(API_ENDPOINTS.AUTH.LOGOUT);
  }

  // 视频相关
  static async getVideos(params = {}) {
    return get(API_ENDPOINTS.VIDEOS.LIST, params);
  }

  static async getVideoDetail(id) {
    return get(API_ENDPOINTS.VIDEOS.DETAIL(id));
  }

  static async reviewVideo(videoId, action, comment = '') {
    return post(API_ENDPOINTS.VIDEOS.REVIEW, { videoId, action, comment });
  }

  static async deleteVideo(id) {
    return del(API_ENDPOINTS.VIDEOS.DELETE(id));
  }

  // 统计相关
  static async getStats() {
    return get(API_ENDPOINTS.STATS.OVERVIEW);
  }

  static async getDailyStats(params = {}) {
    return get(API_ENDPOINTS.STATS.DAILY, params);
  }

  // 日志相关
  static async getVideoLogs(videoId) {
    return get(API_ENDPOINTS.LOGS.VIDEO(videoId));
  }

  static async getReviewerLogs(reviewerId) {
    return get(API_ENDPOINTS.LOGS.REVIEWER(reviewerId));
  }

  // 管理员相关
  static async getAdmins() {
    return get(API_ENDPOINTS.ADMINS.LIST);
  }

  static async createAdmin(data) {
    return post(API_ENDPOINTS.ADMINS.CREATE, data);
  }

  static async updateAdmin(id, data) {
    return put(API_ENDPOINTS.ADMINS.UPDATE(id), data);
  }

  static async deleteAdmin(id) {
    return del(API_ENDPOINTS.ADMINS.DELETE(id));
  }
}

// 导出拦截器，供外部使用
export { interceptor };

// 默认导出
export default {
  get,
  post,
  put,
  del,
  upload,
  ApiService,
  interceptor
};

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  min-width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  font-family: inherit;
}

#root {
  width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 链接样式 */
a {
  font-weight: 500;
  color: #667eea;
  text-decoration: inherit;
}

a:hover {
  color: #5a67d8;
}

/* 标题样式 */
h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* 按钮样式 */
button {
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

button:focus,
button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* 输入框样式 */
input, textarea {
  font-family: inherit;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 视频审核系统 API 测试</h1>
    
    <div class="test-section">
        <h2>1. 健康检查</h2>
        <button onclick="testHealth()">测试 /api/health</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 管理员登录</h2>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="password">
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 获取视频列表</h2>
        <button onclick="testGetVideos()">获取视频列表</button>
        <div id="videos-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 获取统计信息</h2>
        <button onclick="testGetStats()">获取统计信息</button>
        <div id="stats-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 小程序接口测试</h2>
        <input type="text" id="userId" placeholder="用户ID" value="test-user-123">
        <button onclick="testGetMyVideos()">获取我的视频</button>
        <div id="my-videos-result" class="result"></div>
    </div>

    <script>
        let authToken = null;

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function testHealth() {
            try {
                const response = await fetch('http://localhost:3000/api/health');
                const data = await response.json();
                showResult('health-result', {
                    status: response.status,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('health-result', {
                    error: error.message
                }, false);
            }
        }

        async function testLogin() {
            try {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                const response = await fetch('http://localhost:3000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    authToken = data.token;
                }
                
                showResult('login-result', {
                    status: response.status,
                    data: data,
                    token_saved: !!authToken
                }, response.ok);
            } catch (error) {
                showResult('login-result', {
                    error: error.message
                }, false);
            }
        }

        async function testGetVideos() {
            try {
                if (!authToken) {
                    showResult('videos-result', {
                        error: '请先登录获取token'
                    }, false);
                    return;
                }

                const response = await fetch('http://localhost:3000/api/admin/videos', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                showResult('videos-result', {
                    status: response.status,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('videos-result', {
                    error: error.message
                }, false);
            }
        }

        async function testGetStats() {
            try {
                if (!authToken) {
                    showResult('stats-result', {
                        error: '请先登录获取token'
                    }, false);
                    return;
                }

                const response = await fetch('http://localhost:3000/api/admin/stats', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                showResult('stats-result', {
                    status: response.status,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('stats-result', {
                    error: error.message
                }, false);
            }
        }

        async function testGetMyVideos() {
            try {
                const userId = document.getElementById('userId').value;
                
                const response = await fetch(`http://localhost:3000/api/my/videos?userId=${userId}`);
                const data = await response.json();
                
                showResult('my-videos-result', {
                    status: response.status,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('my-videos-result', {
                    error: error.message
                }, false);
            }
        }

        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>

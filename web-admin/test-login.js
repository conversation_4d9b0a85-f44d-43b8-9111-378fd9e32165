const { API_ENDPOINTS, buildUrl } = require('./src/config/api');
async function testLogin() {
  console.log('🧪 开始测试登录功能...');
  
  try {
    // 测试登录接口
    const response = await fetch(buildUrl(API_ENDPOINTS.AUTH.LOGIN), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        username: 'admin', 
        password: 'password' 
      }),
    });

    console.log('📡 响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ 登录失败:', errorData);
      return false;
    }

    const data = await response.json();
    console.log('✅ 登录成功:', data);
    
    if (data.token) {
      console.log('🔑 Token获取成功:', data.token);
      
      // 测试使用token访问管理接口
      const adminResponse = await fetch('http://localhost:3000/api/admin/videos', {
        headers: {
          'Authorization': `Bearer ${data.token}`,
        },
      });
      
      console.log('📊 管理接口响应状态:', adminResponse.status);
      
      if (adminResponse.ok) {
        const videos = await adminResponse.json();
        console.log('✅ 管理接口访问成功，视频数量:', videos.length);
        return true;
      } else {
        console.error('❌ 管理接口访问失败');
        return false;
      }
    } else {
      console.error('❌ 未获取到token');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testLogin;
}

// 如果在浏览器中运行
if (typeof window !== 'undefined') {
  window.testLogin = testLogin;
}
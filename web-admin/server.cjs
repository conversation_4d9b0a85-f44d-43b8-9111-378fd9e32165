const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const knex = require('knex');
const cors = require('cors');

const app = express();
const port = 3000;

// --- 数据库配置 (Knex + SQLite) ---
const db = knex({
  client: 'sqlite3',
  connection: {
    filename: path.join(__dirname, 'db.sqlite3'),
  },
  useNullAsDefault: true,
});

// 启动时检查并创建数据表
async function setupDatabase() {
  try {
    // 创建videos表
    const videosTableExists = await db.schema.hasTable('videos');
    if (!videosTableExists) {
      console.log('Creating videos table...');
      await db.schema.createTable('videos', (table) => {
        table.increments('id').primary();
        table.string('filename').notNullable();
        table.string('url').notNullable();
        table.string('user_id').notNullable();
        table.string('title');
        table.string('status').defaultTo('pending');
        table.integer('review_deadline_hours').defaultTo(72);
        table.timestamps(true, true);
      });
      console.log('Videos table created.');
    }

    // 创建管理员表
    const adminsTableExists = await db.schema.hasTable('admins');
    if (!adminsTableExists) {
      console.log('Creating admins table...');
      await db.schema.createTable('admins', (table) => {
        table.increments('id').primary();
        table.string('username').unique().notNullable();
        table.string('password').notNullable();
        table.timestamps(true, true);
      });

      // 插入默认管理员
      await db('admins').insert({
        username: 'admin',
        password: 'password'
      });
      console.log('Admins table created with default admin.');
    }

    // 创建审核记录表
    const reviewLogsTableExists = await db.schema.hasTable('review_logs');
    if (!reviewLogsTableExists) {
      console.log('Creating review_logs table...');
      await db.schema.createTable('review_logs', (table) => {
        table.increments('id').primary();
        table.integer('video_id').notNullable();
        table.integer('reviewer_id').notNullable();
        table.string('reviewer_username').notNullable();
        table.string('action').notNullable();
        table.string('previous_status');
        table.string('new_status').notNullable();
        table.text('comment');
        table.string('ip_address');
        table.timestamps(true, true);
      });
      console.log('Review logs table created.');
    }

    console.log('✅ 数据库初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  }
}

// --- Multer 文件上传配置 ---
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExtension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + fileExtension);
  },
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传视频文件'));
    }
  }
});

// --- 中间件 ---
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// --- 认证中间件 ---
const AUTH_TOKEN = 'supersecrettoken';

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) {
    return res.status(401).json({ message: '未授权: 缺少 token' });
  }

  if (token === AUTH_TOKEN) {
    next();
  } else {
    res.status(403).json({ message: '未授权: 无效 token' });
  }
}

// --- API 路由 ---

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 1. 小程序上传视频
app.post('/api/upload', upload.single('videoFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: '没有上传文件' });
    }

    const { userId, title } = req.body;
    if (!userId) {
      return res.status(400).json({ message: '缺少用户ID' });
    }

    const videoUrl = `/uploads/${req.file.filename}`;
    
    const [videoId] = await db('videos').insert({
      filename: req.file.filename,
      url: videoUrl,
      user_id: userId,
      title: title || '未命名视频',
      status: 'pending'
    });

    console.log(`✅ 视频上传成功: ${req.file.filename}, 用户: ${userId}`);
    
    res.status(200).json({
      message: '上传成功',
      videoId: videoId,
      filename: req.file.filename,
      url: videoUrl
    });
  } catch (error) {
    console.error('❌ 上传失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 2. 获取指定用户上传的视频列表 (小程序端使用)
app.get('/api/my/videos', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ message: '缺少 userId' });
    }

    const videos = await db('videos')
      .where('user_id', userId)
      .orderBy('created_at', 'desc');

    res.status(200).json(videos);
  } catch (error) {
    console.error('❌ 获取用户视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 3. 管理员登录
app.post('/api/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }

    const admin = await db('admins')
      .where({ username, password })
      .first();

    if (admin) {
      res.status(200).json({
        message: '登录成功',
        token: AUTH_TOKEN,
        admin: {
          id: admin.id,
          username: admin.username
        }
      });
    } else {
      res.status(401).json({ message: '用户名或密码错误' });
    }
  } catch (error) {
    console.error('❌ 登录失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 4. 获取所有视频列表 (管理员端使用，需要认证)
app.get('/api/admin/videos', authenticateToken, async (req, res) => {
  try {
    const { status, search, limit = 200, offset = 0, sortBy = 'smart' } = req.query;
    
    let query = db('videos');
    
    // 状态筛选
    if (status && status !== 'all' && status !== '') {
      query = query.where('status', status);
    }
    
    // 搜索功能
    if (search) {
      query = query.where(function() {
        this.where('title', 'like', `%${search}%`)
            .orWhere('user_id', 'like', `%${search}%`)
            .orWhere('filename', 'like', `%${search}%`);
      });
    }
    
    // 智能排序逻辑
    if (sortBy === 'smart') {
      query = query.orderByRaw(`
        CASE 
          WHEN status = 'pending' THEN 1 
          WHEN status = 'approved' THEN 2 
          WHEN status = 'rejected' THEN 3 
          ELSE 4 
        END ASC,
        created_at ASC
      `);
    } else if (sortBy === 'newest') {
      query = query.orderBy('created_at', 'desc');
    } else if (sortBy === 'oldest') {
      query = query.orderBy('created_at', 'asc');
    } else {
      query = query.orderBy('created_at', 'desc');
    }
    
    const videos = await query.limit(parseInt(limit)).offset(parseInt(offset));
    
    // 为每个视频添加审核紧急程度信息
    const videosWithUrgency = videos.map(video => {
      const now = new Date();
      const createdAt = new Date(video.created_at);
      const deadlineHours = video.review_deadline_hours || 72;
      const elapsedHours = (now - createdAt) / (1000 * 60 * 60);
      const remainingHours = Math.max(0, deadlineHours - elapsedHours);
      const urgencyLevel = video.status === 'pending' ? 
        (elapsedHours / deadlineHours > 0.8 ? 'critical' :
         elapsedHours / deadlineHours > 0.5 ? 'high' :
         elapsedHours / deadlineHours > 0.2 ? 'medium' : 'low') : 'none';
      
      return {
        ...video,
        urgency_level: urgencyLevel,
        remaining_hours: Math.round(remainingHours * 10) / 10,
        elapsed_hours: Math.round(elapsedHours * 10) / 10,
        progress_percentage: video.status === 'pending' ? 
          Math.min(100, Math.round((elapsedHours / deadlineHours) * 100)) : 100,
        is_expired: video.status === 'pending' && elapsedHours > deadlineHours
      };
    });
    
    // 获取统计信息
    const stats = await db('videos')
      .select('status')
      .count('* as count')
      .groupBy('status');
    
    const statusStats = { pending: 0, approved: 0, rejected: 0 };
    stats.forEach(stat => {
      statusStats[stat.status] = stat.count;
    });
    
    const urgentCount = videosWithUrgency.filter(v => 
      v.status === 'pending' && (v.urgency_level === 'high' || v.urgency_level === 'critical')
    ).length;
    
    res.status(200).json({
      videos: videosWithUrgency,
      total: videosWithUrgency.length,
      stats: statusStats,
      urgent_count: urgentCount,
      sort_method: sortBy
    });
  } catch (error) {
    console.error('❌ 获取视频列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 5. 审核视频 (管理员端使用，需要认证)
app.post('/api/admin/review', authenticateToken, async (req, res) => {
  try {
    const { videoId, action, comment } = req.body;
    
    if (!videoId || !action) {
      return res.status(400).json({ message: '缺少必要参数' });
    }
    
    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: '无效的审核动作' });
    }
    
    // 获取当前视频状态
    const video = await db('videos').where('id', videoId).first();
    if (!video) {
      return res.status(404).json({ message: '视频不存在' });
    }
    
    const newStatus = action === 'approve' ? 'approved' : 'rejected';
    
    // 更新视频状态
    await db('videos')
      .where('id', videoId)
      .update({ 
        status: newStatus,
        updated_at: new Date()
      });
    
    // 记录审核日志
    await db('review_logs').insert({
      video_id: videoId,
      reviewer_id: 1, // 简化处理，实际应该从token中获取
      reviewer_username: 'admin',
      action: action,
      previous_status: video.status,
      new_status: newStatus,
      comment: comment || '',
      ip_address: req.ip || req.connection.remoteAddress
    });
    
    console.log(`✅ 视频审核完成: ${videoId} -> ${newStatus}`);
    
    res.status(200).json({
      message: '审核完成',
      videoId: videoId,
      newStatus: newStatus
    });
  } catch (error) {
    console.error('❌ 审核失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 6. 获取统计信息
app.get('/api/admin/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await db('videos')
      .select('status')
      .count('* as count')
      .groupBy('status');
    
    const result = {
      pending: 0,
      approved: 0,
      rejected: 0,
      total: 0
    };
    
    stats.forEach(stat => {
      result[stat.status] = stat.count;
      result.total += stat.count;
    });
    
    res.status(200).json(result);
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// --- 启动服务器 ---
async function startServer() {
  try {
    await setupDatabase();
    
    app.listen(port, () => {
      console.log(`🚀 服务器运行在 http://localhost:${port}`);
      console.log(`📡 API健康检查: http://localhost:${port}/api/health`);
      console.log(`🔐 管理员登录: POST http://localhost:${port}/api/admin/login`);
      console.log(`📤 视频上传: POST http://localhost:${port}/api/upload`);
      console.log(`📋 我的视频: GET http://localhost:${port}/api/my/videos?userId=xxx`);
      console.log(`⚙️  管理后台: GET http://localhost:${port}/api/admin/videos`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

startServer();

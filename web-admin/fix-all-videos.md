# 修复后台显示所有视频问题

## 🎯 问题描述
后台审核界面需要显示所有视频（包括待审核、已通过、已拒绝），而不是只显示部分视频。

## 🔧 已实施的修复

### 1. 后端API修复 (server.cjs)
- ✅ 增加默认限制数量到200个视频
- ✅ 修复状态筛选逻辑，确保默认返回所有状态
- ✅ 添加调试日志，便于排查问题

### 2. 前端逻辑修复 (App.jsx)
- ✅ 修复API调用参数，确保正确传递筛选条件
- ✅ 登录后默认加载所有视频 (status='all')
- ✅ 添加强制刷新按钮
- ✅ 增加调试日志

### 3. 界面优化
- ✅ 添加状态筛选按钮：全部、待审核、已通过、已拒绝
- ✅ 添加搜索功能
- ✅ 添加刷新按钮，可手动重新加载所有视频

## 🚀 使用方法

### 启动后端服务器
```bash
cd web-admin
node server.cjs
```

### 测试API (可选)
```bash
cd web-admin
node test-api.js
```

### 前端操作
1. 登录管理后台
2. 默认显示所有视频
3. 使用筛选按钮切换不同状态
4. 使用搜索框查找特定视频
5. 点击"🔄 刷新"按钮强制重新加载

## 🔍 排查步骤

如果仍然看不到所有视频，请按以下步骤排查：

### 1. 检查数据库
```bash
cd web-admin
node -e "
const knex = require('knex');
const db = knex({
  client: 'sqlite3',
  connection: { filename: './db.sqlite3' },
  useNullAsDefault: true,
});
db('videos').select('*').then(videos => {
  console.log('数据库中的视频总数:', videos.length);
  videos.forEach(v => console.log(\`ID: \${v.id}, 标题: \${v.title}, 状态: \${v.status}\`));
  process.exit(0);
});
"
```

### 2. 检查API响应
```bash
curl -H "Authorization: Bearer supersecrettoken" "http://localhost:3000/api/admin/videos"
```

### 3. 检查前端控制台
- 打开浏览器开发者工具
- 查看Console标签页的日志
- 查看Network标签页的API请求

## 📋 预期结果

正确配置后，你应该看到：
- 登录后显示所有视频（包括各种状态）
- 筛选按钮可以正常切换不同状态的视频
- 搜索功能正常工作
- 刷新按钮可以重新加载数据

## 🆘 如果问题仍然存在

1. 重启后端服务器
2. 清除浏览器缓存
3. 点击前端的"🔄 刷新"按钮
4. 检查控制台错误信息
5. 确认数据库中确实有不同状态的视频数据
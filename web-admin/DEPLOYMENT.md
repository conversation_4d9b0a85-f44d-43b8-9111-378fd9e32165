# 视频审核后台管理系统 - 部署指南

## 📋 部署前准备

### 系统要求
- Node.js 14.0.0 或更高版本
- npm 或 yarn
- 至少 1GB 内存
- 至少 5GB 磁盘空间

### 环境变量配置
在生产环境中，建议设置以下环境变量：

```bash
export NODE_ENV=production
export PORT=3000
export AUTH_TOKEN=your-very-secure-token-here
export ADMIN_USERNAME=your-admin-username
export ADMIN_PASSWORD=your-secure-password
```

## 🚀 部署方式

### 方式一：直接部署

1. **构建前端应用**
```bash
npm run build
```

2. **安装生产依赖**
```bash
cp package-production.json package.json
npm install --production
```

3. **启动生产服务器**
```bash
npm start
```

### 方式二：使用 PM2 部署（推荐）

1. **安装 PM2**
```bash
npm install -g pm2
```

2. **创建 PM2 配置文件**
```bash
# ecosystem.config.js
module.exports = {
  apps: [{
    name: 'video-admin',
    script: 'production-server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      AUTH_TOKEN: 'your-secure-token',
      ADMIN_USERNAME: 'admin',
      ADMIN_PASSWORD: 'your-password'
    }
  }]
};
```

3. **启动应用**
```bash
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 方式三：Docker 部署

1. **构建 Docker 镜像**
```bash
docker build -t video-admin-system .
```

2. **使用 docker-compose 启动**
```bash
# 修改 docker-compose.yml 中的环境变量
docker-compose up -d
```

## 🔧 Nginx 反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态文件缓存
    location /uploads/ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔒 安全配置

### 1. 更改默认密码
- 修改 `ADMIN_USERNAME` 和 `ADMIN_PASSWORD`
- 使用强密码

### 2. 更改认证令牌
- 生成安全的 `AUTH_TOKEN`
- 定期更换令牌

### 3. 文件上传安全
- 限制上传文件类型
- 设置文件大小限制
- 定期清理临时文件

## 📊 监控和日志

### 使用 PM2 监控
```bash
pm2 monit
pm2 logs video-admin
```

### 日志文件位置
- 应用日志：`~/.pm2/logs/`
- 系统日志：`/var/log/`

## 🔄 更新部署

1. **停止服务**
```bash
pm2 stop video-admin
```

2. **更新代码**
```bash
git pull origin main
npm run build
```

3. **重启服务**
```bash
pm2 restart video-admin
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
```bash
lsof -i :3000
kill -9 <PID>
```

2. **数据库权限问题**
```bash
chmod 664 db.sqlite3
chown www-data:www-data db.sqlite3
```

3. **上传目录权限**
```bash
mkdir -p uploads
chmod 755 uploads
chown www-data:www-data uploads
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Node.js 版本是否符合要求
2. 端口是否被占用
3. 环境变量是否正确设置
4. 文件权限是否正确

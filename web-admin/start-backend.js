#!/usr/bin/env node

// 启动后端服务器的脚本
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动视频审核后台服务器...');

// 启动后端服务器
const serverProcess = spawn('node', ['server.cjs'], {
  cwd: __dirname,
  stdio: 'inherit'
});

serverProcess.on('error', (err) => {
  console.error('❌ 启动服务器失败:', err);
  process.exit(1);
});

serverProcess.on('close', (code) => {
  console.log(`🔚 服务器进程退出，退出码: ${code}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  serverProcess.kill('SIGTERM');
});

console.log('✅ 后端服务器启动脚本已运行');
console.log('📋 服务器地址: http://localhost:3000');
console.log('🔑 默认管理员账号: admin / password');
console.log('⚡ 按 Ctrl+C 停止服务器');
version: '3.8'

services:
  video-admin:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - AUTH_TOKEN=your-secure-token-here
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=your-secure-password-here
    volumes:
      - ./uploads:/app/uploads
      - ./db.sqlite3:/app/db.sqlite3
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

module.exports = {
  apps: [{
    name: 'video-admin-system',
    script: 'production-server.cjs',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      AUTH_TOKEN: 'your-secure-token-change-this',
      ADMIN_USERNAME: 'admin',
      ADMIN_PASSWORD: 'your-secure-password-change-this'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};

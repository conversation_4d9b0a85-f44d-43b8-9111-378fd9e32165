# 管理后台PHP后台适配说明

## 概述

管理后台已更新配置以连接到新的PHP后台服务，本文档说明更新内容和兼容性情况。

## 配置更新

### 1. API地址更新

**开发环境**:
- 原地址: `http://localhost:3000`
- 新地址: `http://localhost:8000` ✅

**生产环境**:
- 地址保持不变: `https://api.yourdomain.com`
- 需要在服务器上部署PHP后台到此域名

### 2. API接口兼容性对比

| 功能模块 | 原Node.js接口 | PHP后台接口 | 兼容状态 |
|----------|---------------|-------------|----------|
| **基础功能** |
| 健康检查 | `/api/health` | `/api/health` | ✅ 完全兼容 |
| 管理员登录 | `/api/admin/login` | `/api/admin/login` | ✅ 完全兼容 |
| **视频管理** |
| 视频列表 | `/api/admin/videos` | `/api/admin/videos` | ✅ 完全兼容 |
| 视频审核 | `/api/admin/review` | `/api/admin/review` | ✅ 完全兼容 |
| 视频删除 | `/api/admin/videos/:id` | 暂不支持 | ❌ 需要实现 |
| **统计功能** |
| 基础统计 | `/api/admin/stats` | `/api/admin/stats` | ✅ 完全兼容 |
| 日统计 | `/api/admin/stats/daily` | 暂不支持 | ❌ 需要实现 |
| 月统计 | `/api/admin/stats/monthly` | 暂不支持 | ❌ 需要实现 |
| **审核日志** |
| 视频日志 | `/api/admin/logs/video/:id` | `/api/admin/review-logs?videoId=:id` | ✅ 已适配 |
| 审核员日志 | `/api/admin/logs/reviewer/:id` | `/api/admin/review-logs?reviewerId=:id` | ✅ 已适配 |
| **系统设置** |
| 获取设置 | `/api/admin/settings` | `/api/admin/settings` | ✅ 完全兼容 |
| 更新设置 | `/api/admin/settings` | `/api/admin/settings` | ✅ 完全兼容 |
| 过期状态 | 新功能 | `/api/admin/expired-status` | ✅ 新增功能 |
| **管理员管理** |
| 管理员列表 | `/api/admin/admins` | 暂不支持 | ❌ 需要实现 |
| 创建管理员 | `/api/admin/admins` | 暂不支持 | ❌ 需要实现 |
| 更新管理员 | `/api/admin/admins/:id` | 暂不支持 | ❌ 需要实现 |

## 功能影响分析

### ✅ 正常工作的功能
1. **用户登录** - 完全兼容
2. **视频列表查看** - 完全兼容，支持筛选、搜索、排序
3. **视频审核** - 完全兼容，支持批准/拒绝操作
4. **审核记录查看** - 已适配新接口
5. **基础统计** - 完全兼容
6. **系统设置** - 完全兼容，新增过期检查功能

### ⚠️ 部分功能受限
1. **视频删除** - 暂不支持，需要在PHP后台实现
2. **详细统计** - 日/月统计暂不支持
3. **管理员管理** - 暂不支持多管理员功能

### ❌ 暂不支持的功能
1. **WebSocket实时通知** - PHP后台暂不支持
2. **数据导出** - 暂不支持
3. **批量操作** - 暂不支持

## 测试步骤

### 1. 启动PHP后台
```bash
cd php-backend
php -S localhost:8000 -t public
```

### 2. 启动管理后台
```bash
cd web-admin
npm run dev
```

### 3. 功能测试清单

**登录测试**:
- [ ] 使用 admin/password 登录
- [ ] 检查登录状态保持
- [ ] 测试登出功能

**视频管理测试**:
- [ ] 查看视频列表
- [ ] 测试状态筛选（全部/待审核/已批准/已拒绝）
- [ ] 测试搜索功能
- [ ] 测试排序功能（智能/最新/最旧/紧急）
- [ ] 测试视频审核（批准/拒绝）
- [ ] 查看审核记录

**统计功能测试**:
- [ ] 查看基础统计数据
- [ ] 检查紧急视频计数

**系统设置测试**:
- [ ] 查看当前设置
- [ ] 修改审核时效设置
- [ ] 测试过期视频阻止功能
- [ ] 查看过期状态

## 部署注意事项

### 1. 开发环境
确保PHP后台运行在8000端口，管理后台会自动连接。

### 2. 生产环境
1. 部署PHP后台到生产服务器
2. 配置域名指向PHP后台
3. 如需修改域名，更新 `src/config/api.js` 中的生产环境配置
4. 重新构建管理后台：`npm run build`

### 3. 数据迁移
如果从Node.js版本迁移：
1. 复制SQLite数据库文件
2. 复制上传的视频文件
3. 验证数据完整性

## 已知问题和解决方案

### 1. 视频删除功能缺失
**问题**: PHP后台暂未实现视频删除接口
**临时解决方案**: 可以直接在数据库中删除记录
**长期解决方案**: 在PHP后台添加删除接口

### 2. 管理员管理功能缺失
**问题**: PHP后台暂未实现多管理员管理
**临时解决方案**: 使用默认管理员账号
**长期解决方案**: 在PHP后台添加管理员管理接口

### 3. 详细统计功能缺失
**问题**: 缺少日/月统计功能
**临时解决方案**: 使用基础统计
**长期解决方案**: 在PHP后台添加统计接口

## 后续开发计划

### 优先级1（核心功能）
- [ ] 实现视频删除接口
- [ ] 实现管理员管理接口
- [ ] 添加错误处理和用户反馈

### 优先级2（增强功能）
- [ ] 实现详细统计接口
- [ ] 添加批量操作功能
- [ ] 实现数据导出功能

### 优先级3（高级功能）
- [ ] 添加实时通知功能
- [ ] 实现系统监控
- [ ] 添加操作日志

## 回滚方案

如果需要回滚到Node.js版本：

1. **停止PHP服务**
2. **启动Node.js服务**在3000端口
3. **修改API配置**:
   ```javascript
   // src/config/api.js
   development: {
     API_BASE_URL: 'http://localhost:3000',
     WS_BASE_URL: 'ws://localhost:3000'
   }
   ```
4. **重启管理后台**
5. **验证所有功能**

## 技术支持

如遇问题请：
1. 检查PHP后台服务状态
2. 查看浏览器开发者工具网络面板
3. 查看PHP后台日志文件
4. 运行PHP后台测试脚本

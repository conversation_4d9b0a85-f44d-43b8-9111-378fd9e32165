const express = require('express');
const path = require('path');
const cors = require('cors');
const multer = require('multer');
const knex = require('knex');

const app = express();
const port = process.env.PORT || 3000;

// 数据库配置
const db = knex({
  client: 'sqlite3',
  connection: {
    filename: './db.sqlite3'
  },
  useNullAsDefault: true
});

// 文件上传配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// 中间件
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 静态文件服务 - 服务构建后的前端文件
app.use(express.static(path.join(__dirname, 'dist')));

// 认证配置
const AUTH_TOKEN = process.env.AUTH_TOKEN || 'supersecrettoken';

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) return res.status(401).json({ message: '未授权: 缺少 token' });

  if (token === AUTH_TOKEN) {
    next();
  } else {
    return res.status(403).json({ message: '未授权: token 无效' });
  }
}

// 数据库初始化
async function setupDatabase() {
  try {
    const exists = await db.schema.hasTable('videos');
    if (!exists) {
      await db.schema.createTable('videos', function (table) {
        table.increments('id').primary();
        table.string('user_id').notNullable();
        table.string('title');
        table.string('url').notNullable();
        table.string('status').defaultTo('pending');
        table.timestamp('created_at').defaultTo(db.fn.now());
      });
      console.log('Videos table created.');
    } else {
      console.log('Videos table already exists.');
    }
  } catch (error) {
    console.error('Database setup error:', error);
  }
}

// API 路由

// 登录接口
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  
  const adminUsername = process.env.ADMIN_USERNAME || 'admin';
  const adminPassword = process.env.ADMIN_PASSWORD || 'password';

  if (username === adminUsername && password === adminPassword) {
    res.json({ message: '登录成功', token: AUTH_TOKEN });
  } else {
    res.status(401).json({ message: '用户名或密码错误' });
  }
});

// 上传视频接口
app.post('/api/upload', upload.single('video'), async (req, res) => {
  try {
    const { userId, title } = req.body;
    const videoUrl = `http://localhost:${port}/uploads/${req.file.filename}`;
    
    const [id] = await db('videos').insert({
      user_id: userId,
      title: title,
      url: videoUrl,
      status: 'pending'
    });
    
    res.status(201).json({ message: '视频上传成功', id: id, url: videoUrl });
  } catch (error) {
    console.error('上传视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 获取待审核视频列表
app.get('/api/admin/videos', authenticateToken, async (req, res) => {
  try {
    const pendingVideos = await db('videos').where('status', 'pending').orderBy('created_at', 'desc');
    res.status(200).json(pendingVideos);
  } catch (error) {
    console.error('查询待审核视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 审核视频接口
app.post('/api/videos/:id/:action', authenticateToken, async (req, res) => {
  try {
    const { id, action } = req.params;
    const status = action === 'approve' ? 'approved' : 'rejected';
    
    await db('videos').where('id', id).update({ status: status });
    res.status(200).json({ message: `视频已${action === 'approve' ? '批准' : '拒绝'}` });
  } catch (error) {
    console.error('审核视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 获取指定用户上传的视频列表
app.get('/api/my/videos', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ message: '缺少 userId' });
    }
    const userVideos = await db('videos').where('user_id', userId).orderBy('created_at', 'desc');
    res.status(200).json(userVideos);
  } catch (error) {
    console.error('查询用户视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 所有其他路由都返回前端应用
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
async function startServer() {
  await setupDatabase();
  app.listen(port, () => {
    console.log(`生产服务器已启动，正在监听 http://localhost:${port}`);
    console.log(`管理后台地址: http://localhost:${port}`);
  });
}

startServer();

# 宝塔面板PHP配置
# 在宝塔面板 -> 软件商店 -> PHP -> 设置 -> 配置修改中添加以下配置

# 文件上传配置
file_uploads = On
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

# 内存和执行时间
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

# 错误报告（生产环境建议关闭）
display_errors = Off
log_errors = On
error_log = /www/wwwroot/your-domain/php-backend/logs/php_errors.log

# 其他设置
default_charset = "UTF-8"
date.timezone = "Asia/Shanghai"

# SQLite支持
extension=sqlite3
extension=pdo_sqlite

# 文件信息支持
extension=fileinfo

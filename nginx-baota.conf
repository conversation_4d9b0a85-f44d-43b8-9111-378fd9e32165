# 宝塔面板Nginx配置文件
# 适用于视频上传系统

server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    root /www/wwwroot/your-domain/php-backend/public;
    index index.php index.html;
    
    # 增加上传文件大小限制
    client_max_body_size 100M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    
    # 处理API请求
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 处理上传文件访问
    location /uploads/ {
        alias /www/wwwroot/your-domain/php-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 支持视频范围请求（用于视频播放）
        add_header Accept-Ranges bytes;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-74.sock;  # 根据PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 增加PHP执行时间限制
        fastcgi_read_timeout 300s;
        fastcgi_send_timeout 300s;
    }
    
    # 静态文件处理
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全设置
    location ~ /\. {
        deny all;
    }
    
    # 禁止访问敏感文件
    location ~* \.(log|sql|conf)$ {
        deny all;
    }
}

# HTTPS配置（如果需要）
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /www/wwwroot/your-domain/php-backend/public;
    index index.php index.html;
    
    # SSL证书配置（宝塔面板会自动生成）
    ssl_certificate /www/server/panel/vhost/cert/your-domain/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/your-domain/privkey.pem;
    
    # 其他配置与HTTP相同
    client_max_body_size 100M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location /uploads/ {
        alias /www/wwwroot/your-domain/php-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Accept-Ranges bytes;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-74.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300s;
        fastcgi_send_timeout 300s;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~ /\. {
        deny all;
    }
    
    location ~* \.(log|sql|conf)$ {
        deny all;
    }
}

# 宝塔面板部署指南

## 1. 环境要求

- **服务器**: Linux (CentOS/Ubuntu)
- **宝塔面板**: 7.0+
- **PHP**: 7.4+ (推荐8.0)
- **Nginx**: 1.18+
- **必需PHP扩展**: sqlite3, pdo_sqlite, fileinfo

## 2. 安装步骤

### 2.1 创建网站
1. 登录宝塔面板
2. 点击"网站" -> "添加站点"
3. 域名填写你的域名
4. 根目录设置为: `/www/wwwroot/your-domain`
5. PHP版本选择7.4或8.0

### 2.2 上传代码
1. 将 `php-backend` 文件夹上传到 `/www/wwwroot/your-domain/`
2. 确保目录结构如下:
```
/www/wwwroot/your-domain/
├── php-backend/
│   ├── api/
│   ├── config/
│   ├── public/
│   ├── uploads/
│   ├── data/
│   └── logs/
```

### 2.3 设置文件权限
```bash
# 在宝塔面板终端中执行
cd /www/wwwroot/your-domain/
chmod -R 755 php-backend/
chmod -R 777 php-backend/uploads/
chmod -R 777 php-backend/data/
chmod -R 777 php-backend/logs/
```

### 2.4 配置PHP
1. 进入"软件商店" -> "PHP 7.4" -> "设置"
2. 点击"配置修改"，添加以下配置:
```ini
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
```

3. 点击"扩展"，确保以下扩展已安装:
   - sqlite3
   - pdo_sqlite
   - fileinfo

### 2.5 配置Nginx
1. 点击网站设置 -> "配置文件"
2. 添加以下配置:
```nginx
# 在server块中添加
client_max_body_size 100M;
client_body_timeout 300s;

# 修改网站根目录指向
root /www/wwwroot/your-domain/php-backend/public;

# 添加API路由
location /api/ {
    try_files $uri $uri/ /index.php?$query_string;
}

# 添加上传文件访问
location /uploads/ {
    alias /www/wwwroot/your-domain/php-backend/uploads/;
    expires 1y;
    add_header Accept-Ranges bytes;
}
```

### 2.6 修改配置文件
编辑 `php-backend/config/config.php`:
```php
// 修改BASE_URL为你的域名
define('BASE_URL', 'https://your-domain.com');
```

## 3. 测试部署

### 3.1 健康检查
访问: `https://your-domain.com/api/health`
应该返回: `{"status":"ok","message":"服务器运行正常"}`

### 3.2 测试上传
使用测试页面或小程序测试视频上传功能

## 4. 常见问题解决

### 4.1 上传文件过大
- 检查PHP配置中的 `upload_max_filesize` 和 `post_max_size`
- 检查Nginx配置中的 `client_max_body_size`

### 4.2 权限问题
```bash
# 重新设置权限
chown -R www:www /www/wwwroot/your-domain/php-backend/
chmod -R 755 /www/wwwroot/your-domain/php-backend/
chmod -R 777 /www/wwwroot/your-domain/php-backend/uploads/
chmod -R 777 /www/wwwroot/your-domain/php-backend/data/
chmod -R 777 /www/wwwroot/your-domain/php-backend/logs/
```

### 4.3 数据库问题
- 确保 `data` 目录有写权限
- 检查SQLite扩展是否安装

### 4.4 CORS问题
如果小程序访问出现跨域问题，在Nginx配置中添加:
```nginx
add_header Access-Control-Allow-Origin *;
add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
add_header Access-Control-Allow-Headers 'Content-Type, Authorization, X-Requested-With';
```

## 5. 安全建议

1. **修改默认密码**: 修改管理员默认密码
2. **HTTPS**: 启用SSL证书
3. **防火墙**: 配置适当的防火墙规则
4. **备份**: 定期备份数据库和上传文件

## 6. 监控和维护

1. **日志监控**: 定期检查 `logs/app.log` 和 `logs/php_errors.log`
2. **磁盘空间**: 监控上传文件占用的磁盘空间
3. **性能监控**: 使用宝塔面板的监控功能

## 7. 小程序配置

修改小程序 `config.js` 中的生产环境配置:
```javascript
production: {
  apiBase: 'https://your-domain.com',
  debug: false
}
```

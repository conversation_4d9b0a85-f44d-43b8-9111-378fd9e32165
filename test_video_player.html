<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .url-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频播放测试</h1>
        
        <div class="test-info">
            <strong>测试说明：</strong>
            <ul>
                <li>测试视频文件的在线播放功能</li>
                <li>检查HTTP范围请求支持</li>
                <li>验证MIME类型设置</li>
                <li>测试跨域访问</li>
            </ul>
        </div>

        <div>
            <label for="videoUrl">视频URL:</label>
            <input type="text" id="videoUrl" class="url-input" 
                   value="http://localhost:8000/uploads/videoFile-1753249462-600372145.mp4"
                   placeholder="输入视频URL">
            <button onclick="loadVideo()">加载视频</button>
            <button onclick="testVideoInfo()">测试视频信息</button>
        </div>

        <div class="video-container">
            <video id="testVideo" controls preload="metadata">
                <source src="http://localhost:8000/uploads/videoFile-1753249462-600372145.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        </div>

        <div id="testResults"></div>
    </div>

    <script>
        const video = document.getElementById('testVideo');
        const resultsDiv = document.getElementById('testResults');

        // 加载视频
        function loadVideo() {
            const url = document.getElementById('videoUrl').value;
            if (url) {
                video.src = url;
                video.load();
                addResult('info', `正在加载视频: ${url}`);
            }
        }

        // 测试视频信息
        async function testVideoInfo() {
            const url = document.getElementById('videoUrl').value;
            if (!url) {
                addResult('error', '请输入视频URL');
                return;
            }

            try {
                // 测试HEAD请求
                const headResponse = await fetch(url, { method: 'HEAD' });
                addResult('success', `HEAD请求成功 - 状态码: ${headResponse.status}`);
                
                // 显示响应头信息
                const headers = {};
                headResponse.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                addResult('info', `响应头信息:<br><pre>${JSON.stringify(headers, null, 2)}</pre>`);

                // 测试范围请求
                const rangeResponse = await fetch(url, {
                    headers: { 'Range': 'bytes=0-1023' }
                });
                
                if (rangeResponse.status === 206) {
                    addResult('success', '✅ 支持HTTP范围请求 (206 Partial Content)');
                } else {
                    addResult('error', `❌ 不支持HTTP范围请求 - 状态码: ${rangeResponse.status}`);
                }

            } catch (error) {
                addResult('error', `请求失败: ${error.message}`);
            }
        }

        // 添加测试结果
        function addResult(type, message) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        // 视频事件监听
        video.addEventListener('loadstart', () => {
            addResult('info', '📡 开始加载视频...');
        });

        video.addEventListener('loadedmetadata', () => {
            addResult('success', `✅ 视频元数据加载成功 - 时长: ${video.duration.toFixed(2)}秒`);
        });

        video.addEventListener('loadeddata', () => {
            addResult('success', '✅ 视频数据加载成功');
        });

        video.addEventListener('canplay', () => {
            addResult('success', '✅ 视频可以开始播放');
        });

        video.addEventListener('canplaythrough', () => {
            addResult('success', '✅ 视频可以流畅播放');
        });

        video.addEventListener('error', (e) => {
            addResult('error', `❌ 视频加载错误: ${e.message || '未知错误'}`);
        });

        video.addEventListener('play', () => {
            addResult('success', '▶️ 视频开始播放');
        });

        video.addEventListener('pause', () => {
            addResult('info', '⏸️ 视频暂停');
        });

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testVideoInfo();
            }, 1000);
        });
    </script>
</body>
</html>

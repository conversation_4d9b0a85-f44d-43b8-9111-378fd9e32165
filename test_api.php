<?php
/**
 * 测试API接口
 */

// 测试参数验证
echo "=== 测试API参数验证 ===\n";

// 模拟POST数据
$_POST = [
    'userId' => 'test_user_123',
    'title' => '测试视频',
    'name' => '张三',
    'class' => '一年级1班'
];

// 模拟没有文件上传
$_FILES = [];

echo "POST数据:\n";
print_r($_POST);

echo "\nFILES数据:\n";
print_r($_FILES);

// 测试参数获取
$userId = $_POST['userId'] ?? null;
$title = $_POST['title'] ?? '未命名视频';
$name = $_POST['name'] ?? '';
$class = $_POST['class'] ?? '';

echo "\n解析后的参数:\n";
echo "userId: $userId\n";
echo "title: $title\n";
echo "name: $name\n";
echo "class: $class\n";

// 测试验证逻辑
echo "\n=== 参数验证测试 ===\n";

if (!$userId) {
    echo "❌ 缺少用户ID\n";
} else {
    echo "✅ 用户ID验证通过\n";
}

if (empty($name)) {
    echo "❌ 请填写姓名\n";
} else {
    echo "✅ 姓名验证通过\n";
}

if (empty($class)) {
    echo "❌ 请填写班级\n";
} else {
    echo "✅ 班级验证通过\n";
}

// 测试数据库插入数据结构
echo "\n=== 数据库插入数据结构 ===\n";
$insertData = [
    'filename' => 'test-video.mp4',
    'url' => '/uploads/test-video.mp4',
    'user_id' => $userId,
    'title' => $title,
    'name' => $name,
    'class' => $class,
    'status' => 'pending',
    'review_deadline_hours' => 72,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "插入数据结构:\n";
print_r($insertData);

echo "\n✅ 测试完成！\n";
?>

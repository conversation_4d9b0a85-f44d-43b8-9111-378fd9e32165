<!DOCTYPE html>
<html>
<head>
    <title>测试视频上传</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>测试视频上传 - 包含姓名和班级</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div>
            <label>用户ID:</label>
            <input type="text" name="userId" value="test_user_123" required>
        </div>
        <br>
        <div>
            <label>视频标题:</label>
            <input type="text" name="title" value="测试视频" required>
        </div>
        <br>
        <div>
            <label>姓名:</label>
            <input type="text" name="name" value="张三" required>
        </div>
        <br>
        <div>
            <label>班级:</label>
            <input type="text" name="class" value="一年级1班" required>
        </div>
        <br>
        <div>
            <label>视频文件:</label>
            <input type="file" name="videoFile" accept="video/*" required>
        </div>
        <br>
        <button type="submit">上传</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '上传中...';
                
                const response = await fetch('http://localhost:8000/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<p style="color: green;">上传成功！</p><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">上传失败：${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">错误：${error.message}</p>`;
            }
        });
    </script>
</body>
</html>

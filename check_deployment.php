<?php
/**
 * 部署环境检查脚本
 * 用于检查宝塔面板部署环境是否正确配置
 */

echo "=== 部署环境检查 ===\n\n";

// 1. PHP版本检查
echo "1. PHP版本检查:\n";
$phpVersion = phpversion();
echo "   当前PHP版本: $phpVersion\n";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "   ✅ PHP版本符合要求 (>= 7.4.0)\n";
} else {
    echo "   ❌ PHP版本过低，需要7.4.0或更高版本\n";
}

// 2. 必需扩展检查
echo "\n2. PHP扩展检查:\n";
$requiredExtensions = ['sqlite3', 'pdo_sqlite', 'fileinfo'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ $ext 扩展已安装\n";
    } else {
        echo "   ❌ $ext 扩展未安装\n";
    }
}

// 3. 配置检查
echo "\n3. PHP配置检查:\n";
$configs = [
    'upload_max_filesize' => '100M',
    'post_max_size' => '100M',
    'max_execution_time' => '300',
    'memory_limit' => '256M'
];

foreach ($configs as $key => $recommended) {
    $current = ini_get($key);
    echo "   $key: $current (推荐: $recommended)\n";
    
    if ($key === 'upload_max_filesize' || $key === 'post_max_size') {
        $currentBytes = return_bytes($current);
        $recommendedBytes = return_bytes($recommended);
        if ($currentBytes >= $recommendedBytes) {
            echo "   ✅ 配置正确\n";
        } else {
            echo "   ❌ 配置过小\n";
        }
    } elseif ($key === 'max_execution_time' || $key === 'memory_limit') {
        if (intval($current) >= intval($recommended) || $current === '0' || $current === '-1') {
            echo "   ✅ 配置正确\n";
        } else {
            echo "   ❌ 配置过小\n";
        }
    }
}

// 4. 目录权限检查
echo "\n4. 目录权限检查:\n";
$directories = [
    'uploads' => 'php-backend/uploads',
    'data' => 'php-backend/data',
    'logs' => 'php-backend/logs'
];

foreach ($directories as $name => $path) {
    if (is_dir($path)) {
        if (is_writable($path)) {
            echo "   ✅ $name 目录可写\n";
        } else {
            echo "   ❌ $name 目录不可写\n";
        }
    } else {
        echo "   ❌ $name 目录不存在\n";
    }
}

// 5. 数据库连接测试
echo "\n5. 数据库连接测试:\n";
try {
    if (file_exists('php-backend/config/database.php')) {
        require_once 'php-backend/config/config.php';
        require_once 'php-backend/config/database.php';
        
        $db = new Database();
        echo "   ✅ 数据库连接成功\n";
        
        // 检查表结构
        $stmt = $db->query("PRAGMA table_info(videos)");
        $columns = $stmt->fetchAll();
        
        $hasName = false;
        $hasClass = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'name') $hasName = true;
            if ($column['name'] === 'class') $hasClass = true;
        }
        
        echo "   " . ($hasName ? "✅" : "❌") . " name字段存在\n";
        echo "   " . ($hasClass ? "✅" : "❌") . " class字段存在\n";
        
    } else {
        echo "   ❌ 数据库配置文件不存在\n";
    }
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 6. 网络访问测试
echo "\n6. 网络访问测试:\n";
$baseUrl = $_SERVER['HTTP_HOST'] ?? 'localhost';
$protocol = isset($_SERVER['HTTPS']) ? 'https' : 'http';
$healthUrl = "$protocol://$baseUrl/api/health";

echo "   测试URL: $healthUrl\n";

// 7. 环境变量检查
echo "\n7. 环境变量检查:\n";
echo "   SERVER_SOFTWARE: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "   DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "   HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "\n";

echo "\n=== 检查完成 ===\n";

// 辅助函数：将字符串大小转换为字节
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = intval($val);
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>

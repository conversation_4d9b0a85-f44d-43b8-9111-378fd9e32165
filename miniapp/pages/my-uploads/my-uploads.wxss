/* pages/my-uploads/my-uploads.wxss */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-container {
  padding: 40rpx 30rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: white;
  text-align: center;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

/* 加载和空状态 */
.loading-wrapper, .empty-wrapper {
  text-align: center;
  padding-top: 100rpx; /* 调整内边距 */
  color: #6c757d; /* 柔和颜色 */
  font-size: 28rpx;
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.upload-redirect-btn {
  margin-top: 40rpx;
  width: 300rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #007bff; /* 蓝色主色调 */
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 15rpx rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease-in-out;
}

.upload-redirect-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(0, 123, 255, 0.1);
}

/* 视频列表 */
.video-list {
  width: 100%;
}

.video-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.video-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.2);
}

.video-thumbnail {
  width: 180rpx; /* 缩略图大小 */
  height: 135rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #e9ecef;
  flex-shrink: 0;
  object-fit: cover;
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 135rpx; /* 与缩略图高度一致 */
}

.video-title {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 30rpx;
  color: #343a40;
  font-weight: 500;
  line-height: 1.3;
}

.status-badge {
  display: inline-block;
  padding: 6rpx 15rpx;
  font-size: 24rpx;
  border-radius: 15rpx;
  color: #fff;
  font-weight: 500;
  margin-top: 8rpx;
}

.status-pending {
  background-color: #ffc107; /* 橙色 */
}

.status-approved {
  background-color: #28a745; /* 绿色 */
}

.status-rejected {
  background-color: #dc3545; /* 红色 */
}

.upload-time {
  font-size: 24rpx;
  color: #6c757d;
  align-self: flex-end;
  margin-left: 15rpx;
}

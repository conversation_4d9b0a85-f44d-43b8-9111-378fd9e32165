// pages/my-uploads/my-uploads.js
const { generateUserId, ApiService } = require('../../config.js');

Page({
  data: {
    videos: [],
    loading: true,
    statusMap: {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    }
  },

  onLoad(options) {
    this.fetchMyUploads();
  },

  onPullDownRefresh() {
    this.fetchMyUploads(() => {
      wx.stopPullDownRefresh();
    });
  },

  async fetchMyUploads(callback) {
    this.setData({ loading: true });

    try {
      // 获取用户ID
      const userId = generateUserId();

      // 使用封装好的 ApiService
      const videos = await ApiService.getMyVideos(userId);

      console.log('✅ 获取视频列表成功:', videos);

      // 格式化视频数据
      const formattedVideos = videos.map(video => {
        video.created_at_formatted = new Date(video.created_at).toLocaleString('zh-CN');
        return video;
      });

      this.setData({ videos: formattedVideos });

    } catch (error) {
      console.error('❌ 获取视频列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ loading: false });
      if (typeof callback === 'function') {
        callback();
      }
    }
  },

  goToUpload() {
    wx.switchTab({ // 或者使用 navigateTo，取决于你的 tab-bar 配置
      url: '/pages/upload/upload'
    });
  }
});

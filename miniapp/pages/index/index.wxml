<!--pages/upload/upload.wxml-->
<view class="page-container">
  <view class="upload-wrapper">
    <!-- 未选择视频时，显示选择区域 -->
    <view wx:if="{{!tempFilePath}}" class="video-selector" bindtap="chooseVideo">
      <image class="icon" src="/static/images/icon-add.png"/>
      <text class="tip">点击选择视频</text>
    </view>

    <!-- 已选择视频时，显示预览区域 -->
    <view wx:if="{{tempFilePath}}" class="video-preview-wrapper">
      <video id="myVideo" src="{{tempFilePath}}" controls poster="{{poster}}"></video>
      <view class="re-choose-btn" bindtap="chooseVideo">重新选择</view>
    </view>
  </view>

  <!-- 视频信息输入 -->
  <view class="form-wrapper">
    <view class="form-item">
      <text class="label">视频标题</text>
      <input class="input" placeholder="给你的视频起个名字吧" bindinput="onTitleInput" value="{{title}}"/>
    </view>
    <view class="form-item">
      <text class="label">姓名</text>
      <input class="input" placeholder="请输入您的姓名" bindinput="onNameInput" value="{{name}}"/>
    </view>
    <view class="form-item">
      <text class="label">班级</text>
      <input class="input" placeholder="请输入您的班级" bindinput="onClassInput" value="{{class}}"/>
    </view>
  </view>

  <!-- 上传操作区域 -->
  <view class="action-wrapper">
    <!-- 上传进度条 -->
    <view class="progress-box" wx:if="{{uploadProgress > 0}}">
      <progress percent="{{uploadProgress}}" activeColor="#1AAD19" stroke-width="4" active-mode="forwards"/>
      <text class="progress-text">{{uploadProgress}}%</text>
    </view>

    <button 
      class="upload-btn" 
      type="primary" 
      bindtap="uploadVideo" 
      loading="{{isUploading}}" 
      disabled="{{isUploading || !tempFilePath || !name || !class}}">
      {{ isUploading ? '正在上传' : '确认上传' }}
    </button>
  </view>

  <view class="footer-tip">请上传健康、文明、积极向上的内容</view>
</view>
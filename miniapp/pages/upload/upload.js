// pages/upload/upload.js
const { generateUserId, ApiService } = require('../../config.js');

Page({
  data: {
    tempFilePath: '', // 临时视频路径
    poster: '', // 视频封面
    title: '', // 视频标题
    name: '', // 姓名
    class: '', // 班级
    isUploading: false, // 是否正在上传
    uploadProgress: 0, // 上传进度
  },

  /**
   * 监听标题输入
   */
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 监听姓名输入
   */
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  /**
   * 监听班级输入
   */
  onClassInput(e) {
    this.setData({
      class: e.detail.value
    });
  },

  /**
   * 选择视频
   */
  chooseVideo() {
    if (this.data.isUploading) return;

    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        console.log("选择视频成功", res);
        const tempFile = res.tempFiles[0];
        this.setData({
          tempFilePath: tempFile.tempFilePath,
          poster: tempFile.thumbTempFilePath, // 获取视频封面
        });
      },
      fail: (err) => {
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({ title: '选择视频失败', icon: 'none' });
        }
      }
    });
  },

  /**
   * 上传视频
   */
  async uploadVideo() {
    if (!this.data.tempFilePath) {
      wx.showToast({ title: '请先选择视频', icon: 'none' });
      return;
    }
    if (!this.data.title.trim()) {
      wx.showToast({ title: '请填写视频标题', icon: 'none' });
      return;
    }
    if (!this.data.name.trim()) {
      wx.showToast({ title: '请填写姓名', icon: 'none' });
      return;
    }
    if (!this.data.class.trim()) {
      wx.showToast({ title: '请填写班级', icon: 'none' });
      return;
    }

    this.setData({ isUploading: true, uploadProgress: 0 });

    try {
      // 获取用户ID
      const userId = generateUserId();

      // 使用封装好的 ApiService
      const result = await ApiService.uploadVideo(
        this.data.tempFilePath,
        userId,
        this.data.title,
        this.data.name,
        this.data.class,
        (res) => {
          this.setData({ uploadProgress: res.progress });
        }
      );

      console.log('✅ 上传成功:', result);
      wx.showToast({ title: '上传成功，等待审核', icon: 'success' });
      this.resetForm();

      // 跳转到“我的上传”页面，查看状态
      wx.switchTab({ url: '/pages/my-uploads/my-uploads' });

    } catch (error) {
      console.error('❌ 上传失败:', error);
      this.handleUploadError(error.message || '上传失败，请稍后重试');
    } finally {
      this.setData({ isUploading: false });
    }
  },

  /**
   * 处理上传错误
   */
  handleUploadError(errorMsg) {
    console.error('上传失败:', errorMsg);
    wx.showToast({
      title: errorMsg || '上传失败，请稍后重试',
      icon: 'none',
      duration: 3000
    });
    this.setData({ uploadProgress: 0 });
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      tempFilePath: '',
      poster: '',
      title: '',
      name: '',
      class: '',
      uploadProgress: 0
    });
  }
});
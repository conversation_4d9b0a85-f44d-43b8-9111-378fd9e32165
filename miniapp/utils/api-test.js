// 小程序API测试工具
const { getConfig, request, uploadFile } = require('../config.js');

/**
 * 测试API连接
 */
async function testConnection() {
  try {
    const config = getConfig();
    console.log('🔗 测试连接:', config.apiBase);
    
    // 测试基础连接
    const result = await request({
      url: '/api/health',
      method: 'GET'
    });
    
    console.log('✅ 连接成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 连接失败:', error);
    return false;
  }
}

/**
 * 测试获取视频列表
 */
async function testGetVideos(userId) {
  try {
    console.log('📋 测试获取视频列表, userId:', userId);
    
    const videos = await request({
      url: '/api/my/videos',
      method: 'GET',
      data: { userId }
    });
    
    console.log('✅ 获取成功，视频数量:', videos.length);
    console.log('📹 视频列表:', videos);
    return videos;
  } catch (error) {
    console.error('❌ 获取失败:', error);
    return [];
  }
}

/**
 * 显示当前配置信息
 */
function showConfig() {
  const config = getConfig();
  console.log('⚙️ 当前配置:');
  console.log('   API地址:', config.apiBase);
  console.log('   调试模式:', config.debug);
  
  try {
    const accountInfo = wx.getAccountInfoSync();
    console.log('   环境版本:', accountInfo.miniProgram.envVersion);
  } catch (error) {
    console.log('   环境版本: 无法获取');
  }
}

/**
 * 一键测试所有功能
 */
async function runAllTests() {
  console.log('🚀 开始API测试...');
  
  // 显示配置
  showConfig();
  
  // 测试连接
  const connected = await testConnection();
  if (!connected) {
    console.log('❌ 连接失败，请检查服务器状态');
    return;
  }
  
  // 测试获取视频列表
  const userId = 'test-user-' + Date.now();
  await testGetVideos(userId);
  
  console.log('✅ 所有测试完成');
}

module.exports = {
  testConnection,
  testGetVideos,
  showConfig,
  runAllTests
};

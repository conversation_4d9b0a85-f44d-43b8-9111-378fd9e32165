// 小程序配置文件
var config = {
  // 开发环境配置 - PHP后台
  development: {
    apiBase: 'http://localhost:8000',  // 🔥 已更新为PHP后台端口
    debug: true
  },

  // 生产环境配置 - PHP后台
  production: {
    apiBase: 'http://video2.chinamarathonmajors.com.cn',  // 🔥 部署时修改这里为您的域名
    debug: false
  }
};

// API 端点配置
var API_ENDPOINTS = {
  // 用户相关接口
  USER: {
    UPLOAD_VIDEO: '/api/upload',
    MY_VIDEOS: '/api/my/videos',
    VIDEO_DETAIL: function(id) { return '/api/my/videos/' + id; }
  },

  // 系统接口
  SYSTEM: {
    HEALTH: '/api/health',
    CONFIG: '/api/config'
  }
};

/**
 * 获取当前环境配置
 */
function getConfig() {
  try {
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    
    // develop: 开发版, trial: 体验版, release: 正式版
    if (envVersion === 'release') {
      console.log('🚀 当前环境: 正式版');
      return config.production;
    } else {
      console.log('🔧 当前环境: 开发版');
      return config.development;
    }
  } catch (error) {
    console.warn('获取环境信息失败，使用开发环境配置', error);
    return config.development;
  }
}

/**
 * 生成用户ID
 */
function generateUserId() {
  let userId = wx.getStorageSync('userId');
  if (!userId) {
    // 生成唯一用户ID
    userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    wx.setStorageSync('userId', userId);
    console.log('🆔 生成新用户ID:', userId);
  }
  return userId;
}

/**
 * 构建完整的API URL
 */
function buildApiUrl(endpoint) {
  const currentConfig = getConfig();
  return currentConfig.apiBase + endpoint;
}

/**
 * 统一的网络请求方法
 */
function request(options) {
  const currentConfig = getConfig();

  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: buildApiUrl(options.url),
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (currentConfig.debug) {
          console.log('📡 请求成功:', options.url, res);
        }

        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          const error = new Error(`请求失败: ${res.statusCode}`);
          error.statusCode = res.statusCode;
          error.data = res.data;
          reject(error);
        }
      },
      fail: (err) => {
        if (currentConfig.debug) {
          console.error('📡 请求失败:', options.url, err);
        }
        reject(new Error('网络错误，请检查网络连接'));
      }
    };

    wx.request(requestOptions);
  });
}

/**
 * 统一的文件上传方法
 */
function uploadFile(options) {
  const currentConfig = getConfig();

  return new Promise((resolve, reject) => {
    const uploadTask = wx.uploadFile({
      url: buildApiUrl(options.url),
      filePath: options.filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header: options.header || {},
      success: (res) => {
        if (currentConfig.debug) {
          console.log('📤 上传成功:', options.url, res);
        }

        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (error) {
            resolve(res.data);
          }
        } else {
          let errorMsg = '上传失败';
          try {
            const errorData = JSON.parse(res.data);
            errorMsg = errorData.message || errorMsg;
          } catch (e) {}

          const error = new Error(errorMsg);
          error.statusCode = res.statusCode;
          reject(error);
        }
      },
      fail: (err) => {
        if (currentConfig.debug) {
          console.error('📤 上传失败:', options.url, err);
        }
        reject(new Error('上传失败，请检查网络连接'));
      }
    });

    // 监听上传进度
    if (options.onProgress) {
      uploadTask.onProgressUpdate(options.onProgress);
    }

    return uploadTask;
  });
}

/**
 * API 服务方法
 */
var ApiService = {
  // 上传视频
  uploadVideo: function(filePath, userId, title, name, className, onProgress) {
    return uploadFile({
      url: API_ENDPOINTS.USER.UPLOAD_VIDEO,
      filePath: filePath,
      name: 'videoFile',
      formData: {
        userId: userId,
        title: title,
        name: name,
        class: className
      },
      onProgress: onProgress
    });
  },

  // 获取我的视频列表
  getMyVideos: function(userId) {
    return request({
      url: API_ENDPOINTS.USER.MY_VIDEOS,
      method: 'GET',
      data: { userId: userId }
    });
  },

  // 获取视频详情
  getVideoDetail: function(videoId) {
    return request({
      url: API_ENDPOINTS.USER.VIDEO_DETAIL(videoId),
      method: 'GET'
    });
  },

  // 健康检查
  checkHealth: function() {
    return request({
      url: API_ENDPOINTS.SYSTEM.HEALTH,
      method: 'GET'
    });
  }
};

module.exports = {
  getConfig,
  generateUserId,
  buildApiUrl,
  request,
  uploadFile,
  ApiService,
  API_ENDPOINTS
};

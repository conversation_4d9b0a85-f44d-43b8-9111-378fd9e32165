# PHP后台部署指南

## 从Node.js迁移到PHP

本文档说明如何将原有的Node.js后台服务迁移到PHP版本。

## 迁移对比

### 原Node.js版本
- **框架**: Express.js
- **数据库**: SQLite3 + Knex.js
- **文件上传**: Multer
- **认证**: 简单token认证
- **端口**: 3000

### 新PHP版本
- **框架**: 原生PHP
- **数据库**: SQLite3 + PDO
- **文件上传**: PHP原生上传
- **认证**: 简单token认证
- **端口**: 8000（可配置）

## 功能对比

| 功能 | Node.js版本 | PHP版本 | 状态 |
|------|-------------|---------|------|
| 健康检查 | ✅ | ✅ | 完全兼容 |
| 管理员登录 | ✅ | ✅ | 完全兼容 |
| 视频上传 | ✅ | ✅ | 完全兼容 |
| 用户视频列表 | ✅ | ✅ | 完全兼容 |
| 管理员视频列表 | ✅ | ✅ | 完全兼容 |
| 视频审核 | ✅ | ✅ | 完全兼容 |
| 审核记录 | ✅ | ✅ | 完全兼容 |
| 统计信息 | ✅ | ✅ | 完全兼容 |
| 系统设置 | ✅ | ✅ | 完全兼容 |
| 过期检查 | ✅ | ✅ | 完全兼容 |
| 文件流播放 | ✅ | ✅ | 支持HTTP Range请求 |

## 部署步骤

### 1. 环境准备

确保服务器满足以下要求：
- PHP 7.4+ (推荐PHP 8.0+)
- SQLite3扩展
- fileinfo扩展
- 写入权限

### 2. 文件部署

```bash
# 上传PHP后台文件到服务器
scp -r php-backend/ user@server:/path/to/php-backend/

# 设置目录权限
chmod -R 755 /path/to/php-backend/
chmod -R 777 /path/to/php-backend/uploads/
chmod -R 777 /path/to/php-backend/data/
chmod -R 777 /path/to/php-backend/logs/
```

### 3. 数据迁移

如果需要从现有Node.js版本迁移数据：

```bash
# 复制现有SQLite数据库
cp /path/to/node-backend/db.sqlite3 /path/to/php-backend/data/db.sqlite3

# 复制上传的文件
cp -r /path/to/node-backend/uploads/* /path/to/php-backend/uploads/
```

### 4. 配置修改

编辑 `config/config.php`：

```php
// 修改基础URL
define('BASE_URL', 'https://yourdomain.com');

// 修改认证token（生产环境必须修改）
define('AUTH_TOKEN', 'your-secure-token-here');

// 其他配置...
```

### 5. 启动服务

#### 开发环境
```bash
cd /path/to/php-backend
php start.php localhost 8000
```

#### 生产环境 - Apache

创建虚拟主机配置：

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /path/to/php-backend/public
    
    <Directory /path/to/php-backend/public>
        AllowOverride All
        Require all granted
        
        # 重写规则
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # 安全设置
    <Directory /path/to/php-backend/config>
        Require all denied
    </Directory>
    
    <Directory /path/to/php-backend/data>
        Require all denied
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/php-backend_error.log
    CustomLog ${APACHE_LOG_DIR}/php-backend_access.log combined
</VirtualHost>
```

#### 生产环境 - Nginx

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/php-backend/public;
    index index.php;
    
    # 安全设置
    location ~ ^/(config|data|logs)/ {
        deny all;
        return 404;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 上传文件
    location /uploads/ {
        try_files $uri =404;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 默认路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

### 6. 测试部署

```bash
# 运行测试脚本
php /path/to/php-backend/test.php

# 测试API接口
curl http://yourdomain.com/api/health
curl -X POST -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"password"}' \
     http://yourdomain.com/api/admin/login
```

## 前端配置更新

如果前端（小程序或管理后台）需要更新API地址：

### 小程序端
```javascript
// config.js 或相关配置文件
const API_BASE_URL = 'https://yourdomain.com/api';
```

### 管理后台
```javascript
// 更新API基础地址
const API_BASE_URL = 'https://yourdomain.com/api';
```

## 性能优化

### 1. PHP配置优化

编辑 `php.ini`：
```ini
# 上传限制
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

# 内存和执行时间
memory_limit = 256M
max_execution_time = 300

# OPcache（生产环境推荐）
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

### 2. 数据库优化

```sql
-- 为常用查询添加索引
CREATE INDEX idx_videos_status ON videos(status);
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_created_at ON videos(created_at);
CREATE INDEX idx_review_logs_video_id ON review_logs(video_id);
```

## 监控和日志

### 1. 日志监控
```bash
# 查看应用日志
tail -f /path/to/php-backend/logs/app.log

# 查看Web服务器日志
tail -f /var/log/apache2/php-backend_error.log
tail -f /var/log/nginx/error.log
```

### 2. 健康检查
```bash
# 设置定时健康检查
*/5 * * * * curl -f http://yourdomain.com/api/health || echo "API health check failed" | mail -s "Alert" <EMAIL>
```

## 安全建议

1. **修改默认密码和token**
2. **启用HTTPS**
3. **设置适当的文件权限**
4. **定期备份数据库**
5. **监控异常访问**
6. **限制上传文件类型和大小**

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   chmod -R 777 uploads/ data/ logs/
   ```

2. **PHP扩展缺失**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install php-sqlite3 php-fileinfo
   
   # CentOS/RHEL
   sudo yum install php-pdo php-fileinfo
   ```

3. **数据库连接失败**
   - 检查data目录权限
   - 确认SQLite3扩展已安装

4. **文件上传失败**
   - 检查uploads目录权限
   - 确认PHP上传配置

## 回滚计划

如果需要回滚到Node.js版本：

1. 停止PHP服务
2. 启动Node.js服务
3. 更新前端API地址
4. 恢复数据库（如有必要）

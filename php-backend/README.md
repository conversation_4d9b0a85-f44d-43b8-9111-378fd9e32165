# PHP后台服务

这是将原Node.js后台服务转换为PHP版本的实现。

## 功能特性

- ✅ 视频文件上传
- ✅ 用户视频列表查询
- ✅ 管理员登录认证
- ✅ 视频审核功能
- ✅ 审核记录管理
- ✅ 系统设置管理
- ✅ 过期视频检查
- ✅ 统计信息查询
- ✅ 文件范围请求支持（视频流播放）

## 技术栈

- **PHP**: 7.4+
- **数据库**: SQLite3
- **Web服务器**: PHP内置服务器或Apache/Nginx

## 目录结构

```
php-backend/
├── api/                    # API接口
│   └── index.php          # 主API路由
├── config/                # 配置文件
│   ├── config.php         # 系统配置
│   └── database.php       # 数据库配置
├── middleware/            # 中间件
│   └── auth.php          # 认证中间件
├── utils/                 # 工具类
│   └── upload.php        # 文件上传工具
├── public/               # 公共文件
│   └── index.php         # 入口文件
├── data/                 # 数据目录
│   └── db.sqlite3        # SQLite数据库
├── uploads/              # 上传文件目录
├── logs/                 # 日志目录
└── README.md
```

## 安装和运行

### 1. 环境要求

- PHP 7.4 或更高版本
- SQLite3 扩展
- fileinfo 扩展

### 2. 启动服务

使用PHP内置服务器：

```bash
cd php-backend
php -S localhost:8000 -t public
```

或者使用Apache/Nginx配置虚拟主机指向 `public` 目录。

### 3. 访问服务

- 主页: http://localhost:8000
- API健康检查: http://localhost:8000/api/health
- 上传文件访问: http://localhost:8000/uploads/filename.mp4

## API接口

### 公共接口（无需认证）

#### 健康检查
```
GET /api/health
```

#### 管理员登录
```
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

#### 视频上传
```
POST /api/upload
Content-Type: multipart/form-data

videoFile: [视频文件]
userId: [用户ID]
title: [视频标题]
```

#### 获取用户视频列表
```
GET /api/my/videos?userId=[用户ID]
```

### 管理员接口（需要认证）

所有管理员接口都需要在请求头中包含认证token：
```
Authorization: Bearer supersecrettoken
```

#### 获取所有视频列表
```
GET /api/admin/videos?status=[状态]&search=[搜索]&sortBy=[排序]&limit=[限制]&offset=[偏移]
```

#### 获取统计信息
```
GET /api/admin/stats
```

#### 审核视频
```
POST /api/admin/review
Content-Type: application/json

{
  "videoId": 1,
  "action": "approve|reject",
  "comment": "审核备注"
}
```

#### 获取审核记录
```
GET /api/admin/review-logs?videoId=[视频ID]&reviewerId=[审核员ID]&limit=[限制]&offset=[偏移]
```

#### 获取系统设置
```
GET /api/admin/settings
```

#### 更新系统设置（需要超级管理员权限）
```
PUT /api/admin/settings
Content-Type: application/json

{
  "default_review_deadline_hours": "72",
  "auto_reject_expired": "false",
  "block_upload_when_expired": "false"
}
```

#### 检查过期视频状态
```
GET /api/admin/expired-status
```

## 默认配置

- **默认管理员账号**: admin / password
- **认证Token**: supersecrettoken
- **最大文件大小**: 100MB
- **默认审核时效**: 72小时
- **数据库**: SQLite3 (data/db.sqlite3)

## 数据库表结构

### videos 表
- id: 主键
- filename: 文件名
- url: 访问URL
- user_id: 用户ID
- title: 视频标题
- status: 状态 (pending/approved/rejected)
- review_deadline_hours: 审核时效（小时）
- created_at: 创建时间
- updated_at: 更新时间

### admins 表
- id: 主键
- username: 用户名
- password: 密码
- role: 角色 (admin/super_admin)
- active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间

### settings 表
- id: 主键
- key: 设置键
- value: 设置值
- description: 描述
- created_at: 创建时间
- updated_at: 更新时间

### review_logs 表
- id: 主键
- video_id: 视频ID
- reviewer_id: 审核员ID
- reviewer_username: 审核员用户名
- action: 操作 (approve/reject)
- previous_status: 之前状态
- new_status: 新状态
- comment: 备注
- ip_address: IP地址
- created_at: 创建时间
- updated_at: 更新时间

## 日志

系统日志保存在 `logs/app.log` 文件中，记录重要操作和错误信息。

## 注意事项

1. 确保 `uploads`、`data`、`logs` 目录有写入权限
2. 生产环境中请修改默认的认证token和管理员密码
3. 建议配置适当的文件上传限制和安全策略
4. 定期备份SQLite数据库文件

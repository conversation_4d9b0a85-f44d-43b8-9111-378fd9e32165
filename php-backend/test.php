<?php
/**
 * PHP后台服务测试脚本
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

echo "🧪 开始测试PHP后台服务...\n";
echo str_repeat("-", 50) . "\n";

// 测试数据库连接
echo "1. 测试数据库连接...\n";
try {
    $db = new Database();
    echo "   ✅ 数据库连接成功\n";
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试数据库表
echo "2. 测试数据库表...\n";
$tables = ['videos', 'admins', 'settings', 'review_logs'];
foreach ($tables as $table) {
    try {
        $result = $db->query("SELECT COUNT(*) as count FROM {$table}")->fetch();
        echo "   ✅ {$table}表: {$result['count']} 条记录\n";
    } catch (Exception $e) {
        echo "   ❌ {$table}表错误: " . $e->getMessage() . "\n";
    }
}

// 测试管理员账号
echo "3. 测试管理员账号...\n";
try {
    $admin = $db->selectOne('admins', 'username = ?', ['admin']);
    if ($admin) {
        echo "   ✅ 默认管理员账号存在: {$admin['username']} (角色: {$admin['role']})\n";
    } else {
        echo "   ❌ 默认管理员账号不存在\n";
    }
} catch (Exception $e) {
    echo "   ❌ 管理员账号查询失败: " . $e->getMessage() . "\n";
}

// 测试系统设置
echo "4. 测试系统设置...\n";
try {
    $settings = $db->select('settings');
    echo "   ✅ 系统设置: " . count($settings) . " 项\n";
    foreach ($settings as $setting) {
        echo "      - {$setting['key']}: {$setting['value']}\n";
    }
} catch (Exception $e) {
    echo "   ❌ 系统设置查询失败: " . $e->getMessage() . "\n";
}

// 测试目录权限
echo "5. 测试目录权限...\n";
$directories = [
    'uploads' => UPLOAD_DIR,
    'data' => DATA_DIR,
    'logs' => LOG_DIR
];

foreach ($directories as $name => $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "   ✅ {$name}目录: {$dir} (可写)\n";
    } else {
        echo "   ❌ {$name}目录: {$dir} (不存在或不可写)\n";
    }
}

// 测试配置常量
echo "6. 测试配置常量...\n";
$configs = [
    'BASE_URL' => BASE_URL,
    'MAX_FILE_SIZE' => MAX_FILE_SIZE . ' bytes (' . round(MAX_FILE_SIZE/1024/1024, 1) . 'MB)',
    'AUTH_TOKEN' => substr(AUTH_TOKEN, 0, 10) . '...',
    'DB_PATH' => DB_PATH
];

foreach ($configs as $name => $value) {
    echo "   ✅ {$name}: {$value}\n";
}

// 测试文件上传配置
echo "7. 测试PHP上传配置...\n";
$uploadConfigs = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled'
];

foreach ($uploadConfigs as $name => $value) {
    echo "   ✅ {$name}: {$value}\n";
}

// 测试扩展
echo "8. 测试PHP扩展...\n";
$extensions = ['pdo', 'pdo_sqlite', 'fileinfo', 'json', 'curl'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}: 已加载\n";
    } else {
        echo "   ❌ {$ext}: 未加载\n";
    }
}

// 创建测试数据
echo "9. 创建测试数据...\n";
try {
    // 插入测试视频记录
    $testVideoId = $db->insert('videos', [
        'filename' => 'test-video.mp4',
        'url' => BASE_URL . '/uploads/test-video.mp4',
        'user_id' => 'test-user-123',
        'title' => '测试视频',
        'status' => 'pending',
        'review_deadline_hours' => 72,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    echo "   ✅ 创建测试视频记录: ID {$testVideoId}\n";
    
    // 清理测试数据
    $db->query("DELETE FROM videos WHERE id = ?", [$testVideoId]);
    echo "   ✅ 清理测试数据完成\n";
    
} catch (Exception $e) {
    echo "   ❌ 测试数据操作失败: " . $e->getMessage() . "\n";
}

echo str_repeat("-", 50) . "\n";
echo "🎉 测试完成！\n";
echo "\n";
echo "启动服务命令:\n";
echo "  php start.php [host] [port]\n";
echo "  例如: php start.php localhost 8000\n";
echo "\n";
echo "或者直接使用:\n";
echo "  php -S localhost:8000 -t public\n";
?>

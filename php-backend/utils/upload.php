<?php
/**
 * 文件上传工具类
 */

class FileUpload {
    
    /**
     * 处理视频文件上传
     */
    public static function handleVideoUpload($fileKey = 'videoFile') {
        // 检查POST大小限制
        $postMaxSize = self::parseSize(ini_get('post_max_size'));
        $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;

        if ($contentLength > $postMaxSize) {
            errorResponse('上传文件过大，超过服务器限制 (' . self::formatSize($postMaxSize) . ')', 413);
        }

        // 检查是否有文件上传
        if (!isset($_FILES[$fileKey])) {
            errorResponse('未找到上传文件', 400);
        }

        if ($_FILES[$fileKey]['error'] !== UPLOAD_ERR_OK) {
            $error = $_FILES[$fileKey]['error'];
            errorResponse('文件上传失败: ' . self::getUploadErrorMessage($error), 400);
        }
        
        $file = $_FILES[$fileKey];
        
        // 验证文件大小
        if ($file['size'] > MAX_FILE_SIZE) {
            errorResponse('文件大小超过限制 (' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB)', 400);
        }
        
        // 验证文件类型
        $mimeType = mime_content_type($file['tmp_name']);
        if (!in_array($mimeType, ALLOWED_MIME_TYPES)) {
            errorResponse('不支持的文件类型: ' . $mimeType, 400);
        }
        
        // 生成唯一文件名
        $originalName = $file['name'];
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $uniqueName = $fileKey . '-' . time() . '-' . mt_rand(100000000, 999999999) . '.' . $extension;
        
        // 目标路径
        $targetPath = UPLOAD_DIR . '/' . $uniqueName;
        
        // 移动文件
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            errorResponse('文件保存失败', 500);
        }
        
        // 返回文件信息
        return [
            'filename' => $uniqueName,
            'original_name' => $originalName,
            'size' => $file['size'],
            'mime_type' => $mimeType,
            'url' => BASE_URL . '/uploads/' . $uniqueName,
            'path' => $targetPath
        ];
    }
    
    /**
     * 获取上传错误信息
     */
    private static function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => '文件大小超过php.ini中upload_max_filesize的限制',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过表单中MAX_FILE_SIZE的限制',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
            UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
        ];
        
        return $errors[$errorCode] ?? '未知错误 (' . $errorCode . ')';
    }
    
    /**
     * 删除文件
     */
    public static function deleteFile($filename) {
        $filePath = UPLOAD_DIR . '/' . $filename;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
    
    /**
     * 检查文件是否存在
     */
    public static function fileExists($filename) {
        $filePath = UPLOAD_DIR . '/' . $filename;
        return file_exists($filePath);
    }
    
    /**
     * 获取文件大小
     */
    public static function getFileSize($filename) {
        $filePath = UPLOAD_DIR . '/' . $filename;
        if (file_exists($filePath)) {
            return filesize($filePath);
        }
        return false;
    }
    
    /**
     * 获取文件MIME类型
     */
    public static function getFileMimeType($filename) {
        $filePath = UPLOAD_DIR . '/' . $filename;
        if (file_exists($filePath)) {
            return mime_content_type($filePath);
        }
        return false;
    }
    
    /**
     * 清理过期的临时文件
     */
    public static function cleanupOldFiles($maxAge = 86400) { // 默认24小时
        $files = glob(UPLOAD_DIR . '/*');
        $now = time();
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $fileAge = $now - filemtime($file);
                if ($fileAge > $maxAge) {
                    if (unlink($file)) {
                        $deletedCount++;
                    }
                }
            }
        }
        
        return $deletedCount;
    }

    /**
     * 解析PHP配置中的大小值（如 "8M", "100M"）
     */
    private static function parseSize($size) {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        } else {
            return round($size);
        }
    }

    /**
     * 格式化文件大小显示
     */
    private static function formatSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 获取上传目录信息
     */
    public static function getUploadDirInfo() {
        $totalSize = 0;
        $fileCount = 0;
        $files = glob(UPLOAD_DIR . '/*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
                $fileCount++;
            }
        }
        
        return [
            'total_files' => $fileCount,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'upload_dir' => UPLOAD_DIR
        ];
    }
}

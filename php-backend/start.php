<?php
/**
 * PHP后台服务启动脚本
 */

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die("❌ 需要PHP 7.4或更高版本，当前版本: " . PHP_VERSION . "\n");
}

// 检查必需的扩展
$requiredExtensions = ['pdo', 'fileinfo', 'json'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die("❌ 缺少必需的PHP扩展: " . implode(', ', $missingExtensions) . "\n");
}

// 检查SQLite支持
if (!extension_loaded('pdo_sqlite') && !extension_loaded('sqlite3')) {
    echo "⚠️  警告: 未检测到SQLite扩展，数据库功能可能受限\n";
}

echo "✅ PHP环境检查通过\n";

// 引入配置
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

echo "🔧 正在初始化数据库...\n";

// 初始化数据库
try {
    $db = new Database();
    echo "✅ 数据库初始化完成\n";
} catch (Exception $e) {
    die("❌ 数据库初始化失败: " . $e->getMessage() . "\n");
}

// 检查目录权限
$directories = [
    'uploads' => UPLOAD_DIR,
    'data' => DATA_DIR,
    'logs' => LOG_DIR
];

foreach ($directories as $name => $dir) {
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            die("❌ 无法创建{$name}目录: {$dir}\n");
        }
        echo "✅ 创建{$name}目录: {$dir}\n";
    }
    
    if (!is_writable($dir)) {
        die("❌ {$name}目录不可写: {$dir}\n");
    }
}

echo "✅ 目录权限检查通过\n";

// 获取命令行参数
$host = $argv[1] ?? 'localhost';
$port = $argv[2] ?? '8000';

// 检查端口是否被占用
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    die("❌ 端口 {$host}:{$port} 已被占用\n");
}

echo "🚀 启动PHP后台服务...\n";
echo "📡 服务地址: http://{$host}:{$port}\n";
echo "📋 API文档: http://{$host}:{$port}\n";
echo "🔐 默认管理员: admin / password\n";
echo "🛑 按 Ctrl+C 停止服务\n";
echo str_repeat("-", 50) . "\n";

// 启动内置服务器
$command = "php -d post_max_size=100M -d upload_max_filesize=100M -d max_execution_time=300 -d max_input_time=300 -d memory_limit=256M -d max_file_uploads=20 -S {$host}:{$port} -t public router.php";
$descriptorspec = [
    0 => STDIN,
    1 => STDOUT,
    2 => STDERR
];

$process = proc_open($command, $descriptorspec, $pipes);

if (is_resource($process)) {
    // 等待进程结束
    $return_value = proc_close($process);
    echo "\n🛑 服务已停止\n";
} else {
    die("❌ 无法启动服务\n");
}
?>

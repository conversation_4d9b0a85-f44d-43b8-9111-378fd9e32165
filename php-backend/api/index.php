<?php
/**
 * PHP后台API主入口文件
 */


// 引入配置和依赖
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../utils/upload.php';

// 初始化数据库
$db = new Database();

// 获取请求方法和路径
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api', '', $path); // 移除/api前缀

// 路由处理
try {
    switch ($method . ' ' . $path) {
        
        // 健康检查
        case 'GET /health':
            jsonResponse([
                'status' => 'ok',
                'message' => '服务器运行正常',
                'timestamp' => date('c'),
                'version' => '1.0.0'
            ]);
            break;
            
        // 管理员登录
        case 'POST /admin/login':
            handleAdminLogin($db);
            break;
            
        // 视频上传
        case 'POST /upload':
            handleVideoUpload($db);
            break;
            
        // 获取用户视频列表
        case 'GET /my/videos':
            handleGetMyVideos($db);
            break;
            
        // 获取所有视频列表（管理员）
        case 'GET /admin/videos':
            handleGetAdminVideos($db);
            break;
            
        // 获取统计信息
        case 'GET /admin/stats':
            handleGetStats($db);
            break;
            
        // 审核视频
        case 'POST /admin/review':
            handleReviewVideo($db);
            break;
            
        // 获取审核记录
        case 'GET /admin/review-logs':
            handleGetReviewLogs($db);
            break;
            
        // 获取系统设置
        case 'GET /admin/settings':
            handleGetSettings($db);
            break;
            
        // 更新系统设置
        case 'PUT /admin/settings':
            handleUpdateSettings($db);
            break;
            
        // 检查过期状态
        case 'GET /admin/expired-status':
            handleGetExpiredStatus($db);
            break;
            
        default:
            errorResponse('接口不存在', 404);
    }
    
} catch (Exception $e) {
    logMessage('API错误: ' . $e->getMessage(), 'ERROR');
    errorResponse('服务器内部错误: ' . $e->getMessage(), 500);
}

/**
 * 处理管理员登录
 */
function handleAdminLogin($db) {
    $input = getJsonInput();
    validateRequired($input, ['username', 'password']);
    
    $admin = $db->selectOne('admins', 'username = ? AND password = ? AND active = ?', [
        $input['username'],
        $input['password'],
        1
    ]);
    
    if ($admin) {
        logMessage("管理员登录成功: {$admin['username']}", 'INFO');
        jsonResponse([
            'message' => '登录成功',
            'token' => AUTH_TOKEN,
            'admin' => [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'role' => $admin['role']
            ]
        ]);
    } else {
        logMessage("管理员登录失败: {$input['username']}", 'WARNING');
        errorResponse('用户名或密码错误', 401);
    }
}

/**
 * 处理视频上传
 */
function handleVideoUpload($db) {
    // 获取POST数据
    $userId = $_POST['userId'] ?? null;
    $title = $_POST['title'] ?? '未命名视频';
    
    if (!$userId) {
        errorResponse('缺少用户ID', 400);
    }
    
    // 检查系统设置
    $settings = getSystemSettings($db);
    $blockUpload = ($settings['block_upload_when_expired'] ?? 'false') === 'true';
    $reviewDeadlineHours = intval($settings['default_review_deadline_hours'] ?? 72);
    
    // 如果开启了上传阻止，检查是否有过期的待审核视频
    if ($blockUpload) {
        $expiredVideos = getExpiredVideos($db);
        if (count($expiredVideos) > 0) {
            logMessage("发现 " . count($expiredVideos) . " 个过期待审核视频，阻止新上传", 'WARNING');
            errorResponse(
                "当前有 " . count($expiredVideos) . " 个视频审核超时，暂时无法上传新视频。请联系管理员处理。",
                423,
                'UPLOAD_BLOCKED_EXPIRED_VIDEOS'
            );
        }
    }
    
    // 处理文件上传
    $fileInfo = FileUpload::handleVideoUpload('videoFile');
    
    // 保存到数据库
    $videoId = $db->insert('videos', [
        'filename' => $fileInfo['filename'],
        'url' => $fileInfo['url'],
        'user_id' => $userId,
        'title' => $title,
        'status' => 'pending',
        'review_deadline_hours' => $reviewDeadlineHours,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    logMessage("视频上传成功: {$fileInfo['filename']}, 用户: {$userId}", 'INFO');
    
    successResponse('上传成功，等待审核', [
        'videoId' => $videoId,
        'filename' => $fileInfo['filename'],
        'url' => $fileInfo['url']
    ]);
}

/**
 * 获取用户视频列表
 */
function handleGetMyVideos($db) {
    $userId = $_GET['userId'] ?? null;
    if (!$userId) {
        errorResponse('缺少 userId', 400);
    }
    
    $videos = $db->select('videos', 'user_id = ?', [$userId], 'created_at DESC');
    jsonResponse($videos);
}

/**
 * 获取系统设置
 */
function getSystemSettings($db) {
    $settings = $db->select('settings');
    $result = [];
    foreach ($settings as $setting) {
        $result[$setting['key']] = $setting['value'];
    }
    return $result;
}

/**
 * 获取过期视频
 */
function getExpiredVideos($db) {
    $sql = "SELECT * FROM videos
            WHERE status = 'pending'
            AND datetime(created_at, '+' || review_deadline_hours || ' hours') < datetime('now')";
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

/**
 * 获取管理员视频列表
 */
function handleGetAdminVideos($db) {
    AuthMiddleware::authenticate();

    $status = $_GET['status'] ?? '';
    $search = $_GET['search'] ?? '';
    $limit = intval($_GET['limit'] ?? 200);
    $offset = intval($_GET['offset'] ?? 0);
    $sortBy = $_GET['sortBy'] ?? 'smart';

    // 构建查询
    $whereConditions = [];
    $params = [];

    // 状态筛选
    if ($status && $status !== 'all' && $status !== '') {
        $whereConditions[] = 'status = ?';
        $params[] = $status;
    }

    // 搜索功能
    if ($search) {
        $whereConditions[] = '(title LIKE ? OR user_id LIKE ? OR filename LIKE ?)';
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }

    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

    // 排序逻辑
    $orderBy = '';
    switch ($sortBy) {
        case 'smart':
            $orderBy = "ORDER BY
                CASE
                    WHEN status = 'pending' THEN 1
                    WHEN status = 'approved' THEN 2
                    WHEN status = 'rejected' THEN 3
                    ELSE 4
                END ASC,
                CASE
                    WHEN status = 'pending' THEN
                        (julianday('now') - julianday(created_at)) * 24 / review_deadline_hours
                    ELSE 0
                END DESC,
                created_at ASC";
            break;
        case 'newest':
            $orderBy = 'ORDER BY created_at DESC';
            break;
        case 'oldest':
            $orderBy = 'ORDER BY created_at ASC';
            break;
        case 'urgent':
            $orderBy = "ORDER BY
                CASE
                    WHEN status = 'pending' AND (julianday('now') - julianday(created_at)) * 24 > review_deadline_hours * 0.5 THEN 1
                    WHEN status = 'pending' THEN 2
                    ELSE 3
                END ASC,
                created_at ASC";
            break;
        default:
            $orderBy = 'ORDER BY created_at DESC';
    }

    // 查询视频
    $sql = "SELECT * FROM videos {$whereClause} {$orderBy} LIMIT {$limit} OFFSET {$offset}";
    $stmt = $db->query($sql, $params);
    $videos = $stmt->fetchAll();

    // 为每个视频添加紧急程度信息
    $videosWithUrgency = array_map(function($video) {
        $now = new DateTime();
        $createdAt = new DateTime($video['created_at']);
        $deadlineHours = $video['review_deadline_hours'] ?: 72;
        $elapsedHours = ($now->getTimestamp() - $createdAt->getTimestamp()) / 3600;
        $remainingHours = max(0, $deadlineHours - $elapsedHours);

        $urgencyLevel = 'none';
        if ($video['status'] === 'pending') {
            $progress = $elapsedHours / $deadlineHours;
            if ($progress > 0.8) $urgencyLevel = 'critical';
            elseif ($progress > 0.5) $urgencyLevel = 'high';
            elseif ($progress > 0.2) $urgencyLevel = 'medium';
            else $urgencyLevel = 'low';
        }

        return array_merge($video, [
            'urgency_level' => $urgencyLevel,
            'remaining_hours' => round($remainingHours, 1),
            'elapsed_hours' => round($elapsedHours, 1),
            'progress_percentage' => $video['status'] === 'pending' ?
                min(100, round(($elapsedHours / $deadlineHours) * 100)) : 100,
            'is_expired' => $video['status'] === 'pending' && $elapsedHours > $deadlineHours
        ]);
    }, $videos);

    // 获取统计信息
    $stats = getVideoStats($db);
    $urgentCount = count(array_filter($videosWithUrgency, function($v) {
        return $v['status'] === 'pending' && in_array($v['urgency_level'], ['high', 'critical']);
    }));

    jsonResponse([
        'videos' => $videosWithUrgency,
        'total' => count($videosWithUrgency),
        'stats' => $stats,
        'urgent_count' => $urgentCount,
        'sort_method' => $sortBy
    ]);
}

/**
 * 获取视频统计信息
 */
function getVideoStats($db) {
    $stats = $db->query("SELECT status, COUNT(*) as count FROM videos GROUP BY status")->fetchAll();
    $result = ['pending' => 0, 'approved' => 0, 'rejected' => 0];

    foreach ($stats as $stat) {
        $result[$stat['status']] = intval($stat['count']);
    }

    return $result;
}

/**
 * 处理获取统计信息
 */
function handleGetStats($db) {
    AuthMiddleware::authenticate();

    $stats = getVideoStats($db);
    $stats['total'] = array_sum($stats);

    jsonResponse($stats);
}

/**
 * 处理视频审核
 */
function handleReviewVideo($db) {
    $user = AuthMiddleware::requireAdmin($db);

    $input = getJsonInput();
    validateRequired($input, ['videoId', 'action']);

    $videoId = $input['videoId'];
    $action = $input['action'];
    $comment = $input['comment'] ?? '';

    if (!in_array($action, ['approve', 'reject'])) {
        errorResponse('无效的审核动作', 400);
    }

    // 获取视频当前状态
    $video = $db->selectOne('videos', 'id = ?', [$videoId]);
    if (!$video) {
        errorResponse('视频不存在', 404);
    }

    $previousStatus = $video['status'];
    $newStatus = $action === 'approve' ? 'approved' : 'rejected';

    try {
        // 开始事务
        $db->getPdo()->beginTransaction();

        // 更新视频状态
        $db->update('videos', [
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$videoId]);

        // 记录审核日志
        $db->insert('review_logs', [
            'video_id' => $videoId,
            'reviewer_id' => $user['id'],
            'reviewer_username' => $user['username'],
            'action' => $action,
            'previous_status' => $previousStatus,
            'new_status' => $newStatus,
            'comment' => $comment,
            'ip_address' => getClientIP(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // 提交事务
        $db->getPdo()->commit();

        $actionText = $action === 'approve' ? '批准' : '拒绝';
        logMessage("视频 {$videoId} 被审核员 {$user['username']} {$actionText} ({$previousStatus} -> {$newStatus})", 'INFO');

        jsonResponse([
            'message' => "视频已{$actionText}",
            'videoId' => $videoId,
            'newStatus' => $newStatus,
            'reviewer' => $user['username'],
            'timestamp' => date('c')
        ]);

    } catch (Exception $e) {
        $db->getPdo()->rollBack();
        throw $e;
    }
}

/**
 * 处理获取审核记录
 */
function handleGetReviewLogs($db) {
    AuthMiddleware::authenticate();

    $videoId = $_GET['videoId'] ?? null;
    $reviewerId = $_GET['reviewerId'] ?? null;
    $limit = intval($_GET['limit'] ?? 50);
    $offset = intval($_GET['offset'] ?? 0);

    if ($videoId) {
        // 获取特定视频的审核记录
        $logs = $db->select('review_logs', 'video_id = ?', [$videoId], 'created_at DESC', $limit);
    } elseif ($reviewerId) {
        // 获取特定审核员的记录
        $sql = "SELECT rl.*, v.title as video_title, v.user_id as video_user_id
                FROM review_logs rl
                LEFT JOIN videos v ON rl.video_id = v.id
                WHERE rl.reviewer_id = ?
                ORDER BY rl.created_at DESC
                LIMIT {$limit} OFFSET {$offset}";
        $stmt = $db->query($sql, [$reviewerId]);
        $logs = $stmt->fetchAll();
    } else {
        // 获取所有审核记录
        $sql = "SELECT rl.*, v.title as video_title, v.user_id as video_user_id
                FROM review_logs rl
                LEFT JOIN videos v ON rl.video_id = v.id
                ORDER BY rl.created_at DESC
                LIMIT {$limit} OFFSET {$offset}";
        $stmt = $db->query($sql);
        $logs = $stmt->fetchAll();
    }

    jsonResponse($logs);
}

/**
 * 处理获取系统设置
 */
function handleGetSettings($db) {
    AuthMiddleware::authenticate();

    $settings = getSystemSettings($db);
    jsonResponse($settings);
}

/**
 * 处理更新系统设置
 */
function handleUpdateSettings($db) {
    AuthMiddleware::requireSuperAdmin($db);

    $input = getJsonInput();

    try {
        $db->getPdo()->beginTransaction();

        foreach ($input as $key => $value) {
            $db->update('settings', [
                'value' => strval($value),
                'updated_at' => date('Y-m-d H:i:s')
            ], 'key = ?', ['key' => $key]);
        }

        $db->getPdo()->commit();

        logMessage('系统设置更新成功', 'INFO');
        successResponse('系统设置更新成功');

    } catch (Exception $e) {
        $db->getPdo()->rollBack();
        throw $e;
    }
}

/**
 * 处理获取过期状态
 */
function handleGetExpiredStatus($db) {
    AuthMiddleware::authenticate();

    $settings = getSystemSettings($db);
    $expiredVideos = getExpiredVideos($db);
    $blockUpload = ($settings['block_upload_when_expired'] ?? 'false') === 'true';

    jsonResponse([
        'expiredCount' => count($expiredVideos),
        'blockUpload' => $blockUpload,
        'isBlocked' => $blockUpload && count($expiredVideos) > 0,
        'expiredVideos' => array_map(function($v) {
            return [
                'id' => $v['id'],
                'title' => $v['title'],
                'user_id' => $v['user_id'],
                'created_at' => $v['created_at'],
                'deadline_hours' => $v['review_deadline_hours']
            ];
        }, $expiredVideos)
    ]);
}

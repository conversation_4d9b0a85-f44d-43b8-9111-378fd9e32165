<?php
/**
 * 认证中间件
 */

class AuthMiddleware {
    
    /**
     * 验证token
     */
    public static function authenticate() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        
        if (!$authHeader) {
            errorResponse('未授权: 缺少 token', 401);
        }
        
        // 解析Bearer token
        $parts = explode(' ', $authHeader);
        if (count($parts) !== 2 || $parts[0] !== 'Bearer') {
            errorResponse('未授权: token格式错误', 401);
        }
        
        $token = $parts[1];
        
        if ($token !== AUTH_TOKEN) {
            errorResponse('未授权: 无效 token', 403);
        }
        
        return true;
    }
    
    /**
     * 获取当前认证用户信息（简化版本）
     */
    public static function getCurrentUser($db) {
        // 在实际应用中，应该从token中解析用户信息
        // 这里简化处理，返回第一个活跃的管理员
        $admin = $db->selectOne('admins', 'active = ?', [1]);
        
        if (!$admin) {
            errorResponse('无法获取用户信息', 401);
        }
        
        return $admin;
    }
    
    /**
     * 检查用户权限
     */
    public static function checkPermission($user, $permission) {
        // 简化权限检查，超级管理员拥有所有权限
        if ($user['role'] === 'super_admin') {
            return true;
        }
        
        // 普通管理员的权限检查
        $adminPermissions = [
            'view_videos',
            'review_videos',
            'view_stats'
        ];
        
        return in_array($permission, $adminPermissions);
    }
    
    /**
     * 要求超级管理员权限
     */
    public static function requireSuperAdmin($db) {
        self::authenticate();
        $user = self::getCurrentUser($db);
        
        if ($user['role'] !== 'super_admin') {
            errorResponse('需要超级管理员权限', 403);
        }
        
        return $user;
    }
    
    /**
     * 要求管理员权限
     */
    public static function requireAdmin($db) {
        self::authenticate();
        $user = self::getCurrentUser($db);
        
        if (!in_array($user['role'], ['admin', 'super_admin'])) {
            errorResponse('需要管理员权限', 403);
        }
        
        return $user;
    }
}

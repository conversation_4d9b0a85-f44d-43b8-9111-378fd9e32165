<?php
/**
 * 测试数据库连接和表结构
 */

require_once 'php-backend/config/config.php';
require_once 'php-backend/config/database.php';

echo "=== 测试数据库连接 ===\n";

try {
    $db = new Database();
    echo "✅ 数据库连接成功\n";
    
    // 测试查询videos表结构
    echo "\n=== 检查videos表结构 ===\n";
    $stmt = $db->query("PRAGMA table_info(videos)");
    $columns = $stmt->fetchAll();
    
    echo "videos表字段:\n";
    foreach ($columns as $column) {
        echo "- {$column['name']} ({$column['type']})\n";
    }
    
    // 检查是否有name和class字段
    $hasName = false;
    $hasClass = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'name') $hasName = true;
        if ($column['name'] === 'class') $hasClass = true;
    }
    
    echo "\n=== 字段检查结果 ===\n";
    echo $hasName ? "✅ name字段存在\n" : "❌ name字段不存在\n";
    echo $hasClass ? "✅ class字段存在\n" : "❌ class字段不存在\n";
    
    // 测试插入数据
    echo "\n=== 测试数据插入 ===\n";
    $testData = [
        'filename' => 'test-video-' . time() . '.mp4',
        'url' => '/uploads/test-video-' . time() . '.mp4',
        'user_id' => 'test_user_' . time(),
        'title' => '测试视频标题',
        'name' => '测试姓名',
        'class' => '测试班级',
        'status' => 'pending',
        'review_deadline_hours' => 72,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $videoId = $db->insert('videos', $testData);
    echo "✅ 测试数据插入成功，ID: $videoId\n";
    
    // 查询刚插入的数据
    $video = $db->selectOne('videos', 'id = ?', [$videoId]);
    echo "\n插入的数据:\n";
    print_r($video);
    
    // 清理测试数据
    $db->query("DELETE FROM videos WHERE id = ?", [$videoId]);
    echo "\n✅ 测试数据清理完成\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n✅ 数据库测试完成！\n";
?>
